package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.IntNode;
import com.gommt.hotels.orchestrator.model.request.common.GeoLocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ImageDetails;
import com.gommt.hotels.orchestrator.model.response.listing.MediaDetails;
import com.gommt.hotels.orchestrator.model.response.listing.VideoDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.SubRatingsDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.AdditionalChargesBO;
import com.mmt.hotels.clientgateway.businessobjects.RequestInputBO;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.BNPLDisabledReason;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.request.payment.InsuranceAddOnData;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.CountryWiseReviewData;
import com.mmt.hotels.clientgateway.response.FCBenefit;
import com.mmt.hotels.clientgateway.response.Policy;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.response.availrooms.HotelTag;
import com.mmt.hotels.clientgateway.response.availrooms.MyBizQuickPayConfigBO;
import com.mmt.hotels.clientgateway.response.corporate.CorpAutobookRequestorConfigBO;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseRoomsResponse;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUseSlotPlan;
import com.mmt.hotels.clientgateway.response.listingmap.Poi;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.TagInfo;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.persuasion.response.BgGradient;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.polyglot.TranslationConstant;
import com.mmt.hotels.model.request.AddOnState;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.response.HotelBenefitInfo;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.InsuranceData;
import com.mmt.hotels.model.response.addon.InsuranceDetails;
import com.mmt.hotels.model.response.addon.TmInsuranceAddOns;
import com.mmt.hotels.model.response.addon.WidgetData;
import com.mmt.hotels.model.response.adtech.AdTechDestination;
import com.mmt.hotels.model.response.adtech.AdTechHotel;
import com.mmt.hotels.model.response.adtech.AdTechPaxDetails;
import com.mmt.hotels.model.response.adtech.AdTechSearchContext;
import com.mmt.hotels.model.response.adtech.AdTechSearchContextDetails;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.clientgateway.response.moblanding.LocationDataCG;
import com.mmt.hotels.clientgateway.response.moblanding.InfoItem;
import com.mmt.hotels.model.response.corporate.ApprovingManager;
import com.mmt.hotels.model.response.corporate.AutobookRequestorConfigBO;
import com.mmt.hotels.model.response.corporate.CorpMetaInfo;
import com.mmt.hotels.model.response.corporate.CorpTags;
import com.mmt.hotels.model.response.corporate.ReasonForBooking;
import com.mmt.hotels.model.response.corporate.ReasonForSkipApproval;
import com.mmt.hotels.model.response.corporate.ValidationResponse;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.emi.NoCostEmiDetails;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.listpersonalization.MatchmakerTag;
import com.mmt.hotels.model.response.listpersonalization.PolarisData;
import com.mmt.hotels.model.response.listpersonalization.PolarisTag;
import com.mmt.hotels.model.response.listpersonalization.PriceBucket;
import com.mmt.hotels.model.response.listpersonalization.SpokeCity;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysData;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysPersuasion;
import com.mmt.hotels.model.response.mmtprime.BlackInfo;
import com.mmt.hotels.model.response.mmtprime.BorderGradient;
import com.mmt.hotels.model.response.mypartner.MarkUp;
import com.mmt.hotels.model.response.mypartner.MarkUpDetails;
import com.mmt.hotels.model.response.mypartner.MarkUpType;
import com.mmt.hotels.model.response.nearby.NearByLocation;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard;
import com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse;
import com.mmt.hotels.model.response.persuasion.SelectiveHotelPersuasions;
import com.mmt.hotels.model.response.pricing.AlternatePriceCard;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancellationPolicyTimeline;
import com.mmt.hotels.model.response.pricing.PaymentPlan;
import com.mmt.hotels.model.response.pricing.jsonviews.LongStayBenefits;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.searchwrapper.CollectionsResponseBo;
import com.mmt.hotels.model.response.searchwrapper.GeoLocation;
import com.mmt.hotels.model.response.searchwrapper.RecommendedSearchContext;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.LatLong;
import com.mmt.hotels.model.response.staticdata.poiinfo.Category;
import com.mmt.hotels.model.response.staticdata.poiinfo.Centre;
import com.mmt.hotels.model.response.staticdata.poiinfo.Meta;
import com.mmt.hotels.model.response.staticdata.poiinfo.POIInfo;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.listing.personalization.CardError;
import com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse;
import com.mmt.hotels.pojo.matchmaker.MatchMakerVideoInfo;
import com.mmt.hotels.pojo.response.detail.BhfPersuasion;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.model.AttributesFacility;
import com.mmt.model.CardActionData;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.Item;
import com.mmt.model.PropertyPersuasions;
import com.mmt.model.RoomInfo;
import com.mmt.model.Section;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.ParseException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.filter.FilterGroup.LOCATION;
import static com.mmt.hotels.filter.FilterGroup.STAR_RATING;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommonResponseTransformerTest {

	@InjectMocks
	CommonResponseTransformer commonResponseTransformer;

	@Mock
	PoliciesResponseTransformer policiesResponseTransformer;

	@Spy
	ObjectMapperUtil objectMapperUtil;

	@Spy
	private Utility utility;

	@Mock
	private PolyglotService polyglotService;

	@Mock
	PersuasionUtil persuasionUtil;

	@Mock
	CommonConfigConsul commonConfigConsul;

	@Mock
	SearchHotelsFactory searchHotelsFactory;
	@Spy
	PricingEngineHelper pricingEngineHelper;
	@Spy
	private List<com.mmt.hotels.clientgateway.response.moblanding.CardData> businessIdentificationCards;

	private NumberFormat numberFormatter;
	private Persuasion hotelBenefitPersuasion;

	private Method buildCardSheetMethod;

	private Method buildCardSheetElemMethod;

	private Method buildCardSheetElemSuccessInfoMethod;

	private Method buildCardSheetElemSuccessInfoContentMethod;

	private Method buildCardSheetElemSuccessInfoContentListMethod;

	private Method buildCardSheetElemStyleMethod;

	@Before
	public void init(){
		when(polyglotService.getTranslatedData(anyString())).thenAnswer(new Answer<Object>() {
			@Override
			public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
				return invocationOnMock.getArgument(0);
			}
		});
		businessIdentificationCards  = new ArrayList<>();
		businessIdentificationCards.add(buildBusinessIdentificationCard());
		ReflectionTestUtils.setField(commonResponseTransformer, "businessIdentificationCards", businessIdentificationCards);
		ReflectionTestUtils.setField(commonResponseTransformer, "listingMediaLimit", 5);
		ReflectionTestUtils.setField(commonResponseTransformer, "listingMediaLimitExp", 5);
		Map<String, List<String>> persuasionOrder = new HashMap<>();
		persuasionOrder.put("GCC", Arrays.asList("EXCLUSIVE", "SELECT", "SUPER_PACKAGE", "DISCOUNT"));
		persuasionOrder.put("B2C", Arrays.asList("BLACK", "LONGSTAY", "SUPER_PACKAGE", "DISCOUNT"));
		ReflectionTestUtils.setField(commonResponseTransformer, "detailPagePersuasionOrder", "{\"GCC\":[\"EXCLUSIVE\",\"BLACK\",\"SUPER_PACKAGE\",\"DISCOUNT\"],\"B2C\":[\"BLACK\",\"LONGSTAY\",\"SUPER_PACKAGE\",\"DISCOUNT\"]}");

		// Format the totalCost with commas and without decimals
		numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
		numberFormatter.setMaximumFractionDigits(0); // No decimals
		numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
		ReflectionTestUtils.setField(commonResponseTransformer, "numberFormatter", numberFormatter);
		Map<String, String> mandatoryChargesTransfersShortDescMap = new HashMap<>();
		mandatoryChargesTransfersShortDescMap.put("DROP_OFF", "Included drop-off charges");
		ReflectionTestUtils.setField(commonResponseTransformer, "mandatoryChargesTransfersShortDescMap", mandatoryChargesTransfersShortDescMap);
		ReflectionTestUtils.setField(commonResponseTransformer, "blackPersuasionType", "BLACK");
		ReflectionTestUtils.setField(commonResponseTransformer, "losPersuasionType", "LONGSTAY");
		ReflectionTestUtils.setField(commonResponseTransformer, "discountPersuasionType", "DISCOUNT");
		ReflectionTestUtils.setField(commonResponseTransformer, "superPackagePersuasionType", "SUPER_PACKAGE");

	}

	private com.mmt.hotels.clientgateway.response.moblanding.CardData buildBusinessIdentificationCard() {
		com.mmt.hotels.clientgateway.response.moblanding.CardData cardData = new com.mmt.hotels.clientgateway.response.moblanding.CardData();
		cardData.setSequence(0);
		CardInfo cardInfo = new CardInfo();
		cardInfo.setId("BUSINESS_IDENTIFICATION");
		cardInfo.setSubText("{PERCENTAGE}% cheaper than the regular rates");
		cardInfo.setSubTextReview("cheaper than the regular rates");
		cardInfo.setTemplateId("WORKSTAYS");
		cardInfo.setTitleText("Work Stay Discount Unlocked");
		cardInfo.setBgImageURL("https://promos.makemytrip.com/Hotels_product/b2cbusinessuserbg.png");
		cardInfo.setBorderColor("#d8d8d8");
		cardData.setCardInfo(cardInfo);
		return cardData;
	}

	@Mock
	com.mmt.hotels.pojo.listing.personalization.ListPersonalizationResponse listPersonalizationResponse;

	@Mock
	List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataCBList;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardData cardDataCB;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardCondition cardCondition;

	@Mock
	Filter filter;

	@Mock
	CardPayloadResponse cardPayload;

	@Mock
	GenericCardPayloadData genericCardPayloadData;

	@Mock
	CollectionsResponseBo<SearchWrapperHotelEntityAbridged> searchWrapperHotelEntity;

	@Mock
	com.mmt.hotels.pojo.listing.personalization.CardAction cardAction;

	@Mock
	com.mmt.hotels.model.response.listpersonalization.ContextualFilterData contextualFilterData;

	@Mock
	RoomTypeDetails roomTypeDetails;

	@Mock
	PropertyPersuasions propertyPersuasions;

	@Mock
	RecommendedSearchContext searchContext;

	@Mock
	com.mmt.hotels.filter.FilterRange filterRange;

	@Mock
	private HotelBenefitInfo benefitInfo;

	@Mock
	private List<CardData> businessIdentificationNonRegisteredUserCard;

	@Mock
	private OccassionDetails occassionDetails;

	private Map<String, Integer> mediaLimitMap;

	Gson gson = new Gson();

	private static final String TEST_COUNTRY = "INDIA";
	private static final String TEST_REVIEW_TEXT = "Great reviews from {NATIONALITY}!";
	private static final String TEST_TEXT_COLOR = "#A5572A";

	@Before
	public void setUp() throws Exception {
		MockitoAnnotations.initMocks(this);
		ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
		ReflectionTestUtils.setField(utility, "polyglotService", polyglotService);
		LinkedHashMap<String, Double> discountParameters = new LinkedHashMap<>();
		discountParameters.put("discountPercentThreshold", 0.05);
		discountParameters.put("discountThreshold", 100.00);
		ReflectionTestUtils.setField(commonResponseTransformer, "discountParameters", discountParameters);
		ReflectionTestUtils.setField(utility, "mmtRatingsCountThreshold", 50);
		ReflectionTestUtils.setField(commonResponseTransformer, "safetyCategories", Arrays.asList("MySafety - Safe and Hygienic Stays", "ITC_Hotels_WeAssure", "Safe Tourism", "Pavitr_by_Neemrana", "Thinking_of_You_&_Caring_for_You", "Marriott Cleanliness Council", "We Care Clean", "Radisson Safety Protocol,Accor ALL Safe", "Cleanstay Program", "Go Safe Certification", "MySafety - Audited by Deloitte", "Stay Safe Certification", "Safe Tourism Certification", "SHA Certification", "SG Clean Certification", "Commitment_to_Clean", "ALLSAFE", "Safe_Stays_at_Fortune_Hotels", "Best_Western_We_Care_Clean_Program", "IHCL_Tajness_A_Commitment_Restrengthened", "The_Leelas_Suraksha", "Lead_With_Care,Radisson_Hotels_SGS_Safety_Protocol", "Rest_Assured", "Shangri-La_Cares", "Safety_and_Hygiene_Standards", "Oberoi_Safety_Shield", "The_Trident_Safety_Shield", "IHG_Clean_Promise", "Hyatts_Global_Care_Cleanliness_Commitment", "Hilton_CleanStay", "Dubai Assured", "Certified_Safety"));
		HashMap<String, String> map = new HashMap<>();
		map.put("Radisson_Hotels_SGS_Safety_Protocol", "RADISSON_HOTELS_SGS_SAFETY_PROTOCOL");
		map.put("Couple Friendly", "Couple Friendly");
		map.put("ECO FRIENDLY", "ECO FRIENDLY");
		map.put("MMT Assured", "MMT Assured");
		ReflectionTestUtils.setField(commonResponseTransformer, "categoryTextToCategoryTypeMap", map);
		ReflectionTestUtils.setField(commonResponseTransformer, "hotelCategoryTypesPriority", Arrays.asList("MY_SAFETY", "VERIFIED STAYS ALT ACC", "Couple Friendly", "MMT Assured", "ECO FRIENDLY"));
		ReflectionTestUtils.setField(commonResponseTransformer, "altAccoCategoryTypesPriority", Arrays.asList("MY_SAFETY", "VERIFIED STAYS ALT ACC", "Couple Friendly", "MMT Assured", "ECO FRIENDLY"));
		ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", Arrays.asList("abc", "def"));

		ReflectionTestUtils.setField(commonResponseTransformer, "categoryTypeToCategoryTextMap", map);
		ReflectionTestUtils.setField(commonResponseTransformer, "maxCategoriesAllowed", 3);
		ReflectionTestUtils.setField(commonResponseTransformer,"bnplActiveBookingThreshold",3);

		ReflectionTestUtils.setField(commonResponseTransformer,"personalizedPicksIconUrlTag","{\"PREFERRED_BY_COMPANY\":\"https://promos.makemytrip.com/mybiz/hotels/Preferred.png\"}");
		ReflectionTestUtils.setField(commonResponseTransformer,"personalizedPicksStyleClassTagDesktop","{\"PREFERRED_BY_COMPANY\":\"pc__companyPreferredPerNew\"}");
		ReflectionTestUtils.setField(commonResponseTransformer,"personalizedPicksColorTagApps","{\"PREFERRED_BY_COMPANY\":\"https://promos.makemytrip.com/mybiz/hotels/Preferred.png\"}");
		ReflectionTestUtils.setField(commonResponseTransformer,"pcTopSectionPersuasionDesktopConfig","{\"template\":\"MULTI_PERSUASION_H\",\"data\":[{\"hasAction\":false,\"persuasionType\":\"HEADING\",\"html\":false,\"style\":{\"styleClasses\":[\"pc__locationPerNew\"]},\"id\":\"LOC_PERSUASION_1\",\"text\":\"{HOTEL_TAG}\"}],\"placeholder\":\"SINGLE\"}");
		ReflectionTestUtils.setField(commonResponseTransformer,"pcTopSectionPersuasionAppsConfig","{\"data\":[{\"id\":\"LOC_PERSUASION_1\",\"iconurl\":\"https://promos.makemytrip.com/Hotels_product/Persuasion_Icons/tips-icon.png\",\"text\":\"{HOTEL_TAG}\",\"hasAction\":false,\"style\":{\"textColor\":\"#a5572a\",\"fontSize\":\"MID\",\"fontType\":\"B\",\"iconWidth\": 16,\"iconHeight\": 16},\"multiPersuasionPriority\":0,\"horizontal\":false,\"html\":false}],\"placeholder\":\"PLACEHOLDER_CARD_M6\",\"template\":\"IMAGE_TEXT_H\",\"templateType\":\"DEFAULT\"}");
		ReflectionTestUtils.setField(commonResponseTransformer,"pcTopSectionPersuasionAllowedSections",new HashSet<>(Arrays.asList("PERSONALISED_PICKS_HOTELS","LISTING_MAP")));

		String listingHotelMediaLimit = "{\"ANDROID\":5,\"PWA\":5,\"IOS\":5,\"DESKTOP\":5}";
		mediaLimitMap = gson.fromJson(listingHotelMediaLimit, new TypeToken<Map<String, Integer>>() {
		}.getType());
		ReflectionTestUtils.setField(commonResponseTransformer, "mediaLimitMap", mediaLimitMap);
		ReflectionTestUtils.setField(commonResponseTransformer, "scarcityReviewThreshold", 5);

		String bgGradientBlackPopup = "{\"GOLD\":{\"start\":\"#FAF2E4\",\"end\":\"#FFFFFF\",\"angle\":\"0\"},\"PLATINUM\":{\"start\":\"#E7E7E7\",\"end\":\"#FFFFFF\",\"angle\":\"0\"},\"FALLBACK_BLACK_GRADIENT\":{\"start\":\"#757575\",\"end\":\"#BDBDBD\",\"angle\":\"0\"}}";
		ReflectionTestUtils.setField(commonResponseTransformer, "bgGradientBlackPopupMap", gson.fromJson(bgGradientBlackPopup, new TypeToken<Map<String, BgGradient>>() {
		}.getType()));

		String blackV1TierMapping = "{\"GOLD\":\"PREFERRED\",\"PLATINUM\":\"EXCLUSIVE\"}";
		ReflectionTestUtils.setField(commonResponseTransformer, "blackV1TierMap", gson.fromJson(blackV1TierMapping, new TypeToken<Map<String, String>>() {
		}.getType()));

		Map<String, String> mandatoryChargesDisclaimerMap = new HashMap<>();
		mandatoryChargesDisclaimerMap.put("Type__Transfers__Subtype__ALL", "Free Transfer type");
		ReflectionTestUtils.setField(commonResponseTransformer, "mandatoryChargesDisclaimerMap", mandatoryChargesDisclaimerMap);

		buildCardSheetMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheet", com.mmt.hotels.pojo.listing.personalization.CardSheet.class);
		buildCardSheetMethod.setAccessible(true);

		buildCardSheetElemMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheetElem", com.mmt.hotels.pojo.listing.personalization.CardSheetElem.class);
		buildCardSheetElemMethod.setAccessible(true);

		buildCardSheetElemSuccessInfoMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheetElemSuccessInfo", com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.class);
		buildCardSheetElemSuccessInfoMethod.setAccessible(true);

		buildCardSheetElemSuccessInfoContentMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheetElemSuccessInfoContent", com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.class);
		buildCardSheetElemSuccessInfoContentMethod.setAccessible(true);

		buildCardSheetElemSuccessInfoContentListMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheetElemSuccessInfoContentList", List.class);
		buildCardSheetElemSuccessInfoContentListMethod.setAccessible(true);

		buildCardSheetElemStyleMethod = CommonResponseTransformer.class.getDeclaredMethod("buildCardSheetElemStyle", com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style.class);
		buildCardSheetElemStyleMethod.setAccessible(true);

		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "someController");

	}

	@Test
	public void testBuildBusinessIdentificationCardsNonRegisteredUser_withEmptyCardList() {
		// Arrange
		when(org.springframework.util.CollectionUtils.isEmpty(businessIdentificationNonRegisteredUserCard)).thenReturn(true);

		// Act
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.buildBusinessIdentificationCardsNonRegisteredUser();

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCardsNonRegisteredUser_withNonDetailPageAPI() {
		// Arrange
		when(org.springframework.util.CollectionUtils.isEmpty(businessIdentificationNonRegisteredUserCard)).thenReturn(false);
		when(utility.isDetailPageAPI(anyString())).thenReturn(false);

		// Act
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.buildBusinessIdentificationCardsNonRegisteredUser();

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildCardSheet_withValidCardSheet() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheet cardSheet = new com.mmt.hotels.pojo.listing.personalization.CardSheet();
		cardSheet.setBottomSheet(new com.mmt.hotels.pojo.listing.personalization.CardSheetElem());
		cardSheet.setTopSheet(new com.mmt.hotels.pojo.listing.personalization.CardSheetElem());

		CardSheet result = (CardSheet) buildCardSheetMethod.invoke(commonResponseTransformer, cardSheet);

		assertNotNull(result);
		assertNotNull(result.getBottomSheet());
		assertNotNull(result.getTopSheet());
	}


	@Test
	public void testBuildCardSheet_withNullCardSheet() throws Exception {
		CardSheet result = (CardSheet) buildCardSheetMethod.invoke(commonResponseTransformer, (Object) null);

		assertNull(result);
	}

	@Test
	public void testBuildCardSheet_withEmptyCardSheet() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheet cardSheet = new com.mmt.hotels.pojo.listing.personalization.CardSheet();

		CardSheet result = (CardSheet) buildCardSheetMethod.invoke(commonResponseTransformer, cardSheet);

		assertNotNull(result);
		assertNull(result.getBottomSheet());
		assertNull(result.getTopSheet());
	}

	@Test
	public void testBuildCardSheetElem_withValidCardSheetElem() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem cardSheetElem = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem();
		cardSheetElem.setIconUrl("http://example.com/icon.png");
		cardSheetElem.setText("Sample Text");
		cardSheetElem.setPurpose("Sample Purpose");
		cardSheetElem.setSubText("Sample SubText");
		cardSheetElem.setStyle(new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style());
		cardSheetElem.setSuccessInfo(new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo());

		CardSheetElem result = (CardSheetElem) buildCardSheetElemMethod.invoke(commonResponseTransformer, cardSheetElem);

		assertNotNull(result);
		assertEquals("http://example.com/icon.png", result.getIconUrl());
		assertEquals("Sample Text", result.getText());
		assertEquals("Sample Purpose", result.getPurpose());
		assertEquals("Sample SubText", result.getSubText());
		assertNotNull(result.getStyle());
		assertNotNull(result.getSuccessInfo());
	}

	@Test
	public void testBuildCardSheetElem_withNullCardSheetElem() throws Exception {
		CardSheetElem result = (CardSheetElem) buildCardSheetElemMethod.invoke(commonResponseTransformer, (Object) null);

		assertNull(result);
	}

	@Test
	public void testBuildCardSheetElem_withEmptyCardSheetElem() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem cardSheetElem = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem();

		CardSheetElem result = (CardSheetElem) buildCardSheetElemMethod.invoke(commonResponseTransformer, cardSheetElem);

		assertNotNull(result);
		assertNull(result.getIconUrl());
		assertNull(result.getText());
		assertNull(result.getPurpose());
		assertNull(result.getSubText());
		assertNull(result.getStyle());
		assertNull(result.getSuccessInfo());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfo_withValidSuccessInfo() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo successInfo = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo();
		successInfo.setSubText("Sample SubText");
		successInfo.setText("Sample Text");
		successInfo.setIconUrl("http://example.com/icon.png");
		successInfo.setContent(new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content());

		CardSheetElem.SuccessInfo result = (CardSheetElem.SuccessInfo) buildCardSheetElemSuccessInfoMethod.invoke(commonResponseTransformer, successInfo);

		assertNotNull(result);
		assertEquals("Sample SubText", result.getSubText());
		assertEquals("Sample Text", result.getText());
		assertEquals("http://example.com/icon.png", result.getIconUrl());
		assertNotNull(result.getContent());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfo_withNullSuccessInfo() throws Exception {
		CardSheetElem.SuccessInfo result = (CardSheetElem.SuccessInfo) buildCardSheetElemSuccessInfoMethod.invoke(commonResponseTransformer, (Object) null);

		assertNull(result);
	}

	@Test
	public void testBuildCardSheetElemSuccessInfo_withEmptySuccessInfo() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo successInfo = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo();

		CardSheetElem.SuccessInfo result = (CardSheetElem.SuccessInfo) buildCardSheetElemSuccessInfoMethod.invoke(commonResponseTransformer, successInfo);

		assertNotNull(result);
		assertNull(result.getSubText());
		assertNull(result.getText());
		assertNull(result.getIconUrl());
		assertNull(result.getContent());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContent_withValidContent() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content content = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content();
		content.setText("Sample Text");
		content.setContentList(new ArrayList<>());

		CardSheetElem.SuccessInfo.Content result = (CardSheetElem.SuccessInfo.Content) buildCardSheetElemSuccessInfoContentMethod.invoke(commonResponseTransformer, content);

		assertNotNull(result);
		assertEquals("Sample Text", result.getText());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContent_withNullContent() throws Exception {
		CardSheetElem.SuccessInfo.Content result = (CardSheetElem.SuccessInfo.Content) buildCardSheetElemSuccessInfoContentMethod.invoke(commonResponseTransformer, (Object) null);

		assertNull(result);
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContent_withEmptyContent() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content content = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content();

		CardSheetElem.SuccessInfo.Content result = (CardSheetElem.SuccessInfo.Content) buildCardSheetElemSuccessInfoContentMethod.invoke(commonResponseTransformer, content);

		assertNotNull(result);
		assertNull(result.getText());
		assertNull(result.getContentList());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContentList_withValidContentList() throws Exception {
		List<com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList> contentList = new ArrayList<>();
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList contentItem = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList();
		contentItem.setText("Sample Text");
		contentItem.setIconUrl("http://example.com/icon.png");
		contentList.add(contentItem);

		List<CardSheetElem.SuccessInfo.Content.ContentList> result = (List<CardSheetElem.SuccessInfo.Content.ContentList>) buildCardSheetElemSuccessInfoContentListMethod.invoke(commonResponseTransformer, contentList);

		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Sample Text", result.get(0).getText());
		assertEquals("http://example.com/icon.png", result.get(0).getIconUrl());
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContentList_withEmptyContentList() throws Exception {
		List<com.mmt.hotels.pojo.listing.personalization.CardSheetElem.SuccessInfo.Content.ContentList> contentList = new ArrayList<>();
		List<CardSheetElem.SuccessInfo.Content.ContentList> result = (List<CardSheetElem.SuccessInfo.Content.ContentList>) buildCardSheetElemSuccessInfoContentListMethod.invoke(commonResponseTransformer, contentList);
	}

	@Test
	public void testBuildCardSheetElemSuccessInfoContentList_withNullContentList() throws Exception {
		List<CardSheetElem.SuccessInfo.Content.ContentList> result = (List<CardSheetElem.SuccessInfo.Content.ContentList>) buildCardSheetElemSuccessInfoContentListMethod.invoke(commonResponseTransformer, (Object) null);
		assertNull(result);
	}

	@Test
	public void testBuildCardSheetElemStyle_withValidStyle() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style style = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style();
		style.setBorderColor("red");

		CardSheetElem.Style result = (CardSheetElem.Style) buildCardSheetElemStyleMethod.invoke(commonResponseTransformer, style);

		assertNotNull(result);
		assertEquals("red", result.getBorderColor());
	}

	@Test
	public void testBuildCardSheetElemStyle_withNullStyle() throws Exception {
		CardSheetElem.Style result = (CardSheetElem.Style) buildCardSheetElemStyleMethod.invoke(commonResponseTransformer, (Object) null);

		assertNull(result);
	}

	@Test
	public void testBuildCardSheetElemStyle_withEmptyStyle() throws Exception {
		com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style style = new com.mmt.hotels.pojo.listing.personalization.CardSheetElem.Style();

		CardSheetElem.Style result = (CardSheetElem.Style) buildCardSheetElemStyleMethod.invoke(commonResponseTransformer, style);

		assertNotNull(result);
		assertNull(result.getBorderColor());
	}

	@Test
	public void testBuildTcsAmount_withPositiveTcsAmount() {
		// Arrange
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		displayPriceBrkDwn.setTcsAmount(100.0d);
		displayPriceBrkDwn.setDisplayPrice(1000.0d);

		when(polyglotService.getTranslatedData(ConstantsTranslation.TCS_AMOUNT_LABEL)).thenReturn("TCS Amount");
		when(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TYPE_SUM)).thenReturn("SUM");

		// Act
        Method buildTcsAmountMethod = null;
        try {
            buildTcsAmountMethod = CommonResponseTransformer.class.getDeclaredMethod("buildTcsAmount", List.class, DisplayPriceBreakDown.class);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        buildTcsAmountMethod.setAccessible(true);
        try {
            buildTcsAmountMethod.invoke(commonResponseTransformer, pricingDetails, displayPriceBrkDwn);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }

        // Assert
		assertEquals(1100.0d, displayPriceBrkDwn.getDisplayPrice(), 0.001);
		assertEquals(1, pricingDetails.size());
		PricingDetails tcsAmountDetails = pricingDetails.get(0);
		assertEquals(100.0d, tcsAmountDetails.getAmount(), 0.001);
		assertEquals(Constants.TCS_AMOUNT, tcsAmountDetails.getKey());
		assertEquals("TCS Amount", tcsAmountDetails.getLabel());
		assertEquals("SUM", tcsAmountDetails.getType());
	}

	@Test
	public void testBuildTcsAmount_withZeroTcsAmount() throws Exception {
		// Arrange
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		displayPriceBrkDwn.setTcsAmount(0.0d);
		displayPriceBrkDwn.setDisplayPrice(1000.0d);

		// Act
		Method buildTcsAmountMethod = CommonResponseTransformer.class.getDeclaredMethod("buildTcsAmount", List.class, DisplayPriceBreakDown.class);
		buildTcsAmountMethod.setAccessible(true);
		buildTcsAmountMethod.invoke(commonResponseTransformer, pricingDetails, displayPriceBrkDwn);

		// Assert
		assertEquals(1000.0d, displayPriceBrkDwn.getDisplayPrice(), 0.001);
		assertEquals(0, pricingDetails.size());
	}

	@Test
	public void testHotelCategories() {
		Assert.assertNull(commonResponseTransformer.getHotelCategories(null, false, false));

		Assert.assertNull(commonResponseTransformer.getHotelCategories(new HashSet<>(), false, false));

		Set<String> categories = new HashSet<>();
		categories.add("ITC_Hotels_WeAssure");
		Assert.assertNotNull(commonResponseTransformer.getHotelCategories(categories, false, false));
		Assert.assertEquals(1, commonResponseTransformer.getHotelCategories(categories, false, false).size());
		categories.add("ITC_Hotels_WeAssure");
		categories.add("Pavitr_by_Neemrana");
		Assert.assertEquals(1, commonResponseTransformer.getHotelCategories(categories, false, false).size());

		categories = new HashSet<>();
		categories.add("Couple Friendly");
		categories.add("ECO FRIENDLY");
		categories.add("MMT Assured");
		categories.add("MY_SAFETY");
		categories.add("VERIFIED STAYS ALT ACC");
		categories.add("Radisson_Hotels_SGS_Safety_Protocol");
		Assert.assertNotNull(commonResponseTransformer.getHotelCategories(categories, false, false));
		Assert.assertNotNull(commonResponseTransformer.getHotelCategories(categories, true, false));

	}

	@Test
	public void testGetCouponDetails() {
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		BestCoupon couponInfo = new BestCoupon();
		displayPriceBrkDwn.setCouponInfo(couponInfo);
		List<Coupon> cpnList = commonResponseTransformer.getCouponDetails(displayPriceBrkDwn,false, 0, true);
		Assert.assertNotNull(cpnList);
		cpnList = commonResponseTransformer.getCouponDetails(displayPriceBrkDwn,true, 0, true);
		Assert.assertNotNull(cpnList);

		displayPriceBrkDwn.setCouponInfo(new BestCoupon());
		displayPriceBrkDwn.getCouponInfo().setForexCouponDetails(new ForexCouponDetails());
		displayPriceBrkDwn.getCouponInfo().setForexCashbackAmount(100.0);
		cpnList = commonResponseTransformer.getCouponDetails(displayPriceBrkDwn,false, 1, true);
		Assert.assertNotNull(cpnList);
	}

	@Test
	public void testBuildCancellationTimeline(){
		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		com.mmt.hotels.clientgateway.response.CancellationTimeline resp = commonResponseTransformer.buildCancellationTimeline(cancellationTL, null);
		Assert.assertNotNull(resp);

		resp = commonResponseTransformer.buildCancellationTimeline(cancellationTL, BNPLVariant.BNPL_AT_0);
		Assert.assertNotNull(resp);

		resp = commonResponseTransformer.buildCancellationTimeline(cancellationTL, BNPLVariant.BNPL_AT_1);
		Assert.assertNotNull(resp);
	}

	@Test
	public void testBuildCancellationPolicyTimeline() {
		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTL = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		List<CancellationPolicyTimeline> cancellationPolicyTimelines = new ArrayList<>();
		cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		cancellationTL.setCancellationPolicyTimelineList(cancellationPolicyTimelines);
		cancellationTL.setCardChargeText("card charge text");
		com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline resp = commonResponseTransformer.buildCancellationPolicyTimeline(cancellationTL, false, null);
		Assert.assertNotNull(resp);
		Assert.assertEquals("card charge text", resp.getCardChargeText());

		cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		resp = commonResponseTransformer.buildCancellationPolicyTimeline(cancellationTL, true, null);
		Assert.assertNotNull(resp);
		Assert.assertNull(resp.getCardChargeText());
	}

	@Test
	public void testBuildBNPLDetails() {
		BNPLDetails resp = commonResponseTransformer.buildBNPLDetails(true, "", "", "", "", true, false, null, 10.0);
		Assert.assertNotNull(resp);
		resp = commonResponseTransformer.buildBNPLDetails(false, "", "", "", "", true, false, null, 100.0);
		Assert.assertNull(resp);
	}

	@Test
	public void testGetAddons(){
		List<AddOnNode> addOns = new ArrayList<>();
		AddOnNode addonNode = new AddOnNode();
		Map<String, List<String>> tnc = new HashMap<>();
		List<String> tnclst = new ArrayList<>();
		tnclst.add("tnc text");
		tnc.put("tnc", tnclst );
		addonNode.setTnc(tnc );
		List<GenericCardPayloadData> descriptions = new ArrayList<>();
		GenericCardPayloadData gnrcCardPyld = new GenericCardPayloadData();
		descriptions.add(gnrcCardPyld);
		addonNode.setDescriptions(descriptions);
		addOns.add(addonNode);

		AddOnNode insurance = new AddOnNode();
		insurance.setId("INSURANCE_ID");
		insurance.setInsuranceData(new InsuranceData());
		insurance.getInsuranceData().setTmInsuranceAddOns(new ArrayList<>());
		addOns.add(insurance);

		List<com.mmt.hotels.clientgateway.request.payment.AddOnNode> resp = commonResponseTransformer.getAddons(addOns);
		Assert.assertNotNull(resp);

		insurance.getInsuranceData().getTmInsuranceAddOns().add(new TmInsuranceAddOns());
		resp = commonResponseTransformer.getAddons(addOns);
		Assert.assertNotNull(resp);

		resp = commonResponseTransformer.getAddons(new ArrayList<>());
		Assert.assertNull(resp);

	}

	@Test
	public void buildCorpApprovalInfoTest() {
		CorpMetaInfo corpMetaInfo = new CorpMetaInfo();
		corpMetaInfo.setApprovalRequired(true);
		corpMetaInfo.setValidationPayload(new ValidationResponse());
		corpMetaInfo.getValidationPayload().setBlockOopBooking(1);
		corpMetaInfo.getValidationPayload().setBlockSkipApproval(1);
		corpMetaInfo.setQuickCheckout(true);
		corpMetaInfo.setTags(new ArrayList<>());
		corpMetaInfo.getTags().add(new CorpTags());

		com.mmt.hotels.clientgateway.response.corporate.CorpApprovalInfo resp = commonResponseTransformer.buildCorpApprovalInfo(corpMetaInfo, false);
		Assert.assertNotNull(resp);

		resp = commonResponseTransformer.buildCorpApprovalInfo(null, false);
		Assert.assertNull(resp);
	}

	@Test
	public void buildManagersTest() {
		List<ApprovingManager> managerList = new ArrayList<>();
		managerList.add(new ApprovingManager());
		managerList.get(0).setId(1);
		managerList.get(0).setName("Rishabh");
		managerList.get(0).setBusinessEmailId("<EMAIL>");

		List<com.mmt.hotels.clientgateway.response.corporate.ApprovingManager> resp = commonResponseTransformer.buildManagers(managerList);
		Assert.assertNotNull(resp);
		resp = commonResponseTransformer.buildManagers(new ArrayList<>());
		Assert.assertNull(resp);
	}

	@Test
	public void testBuildRatePlanList(){
		List<RoomType> roomTypes = new ArrayList<>();
		RoomType rmTyp = new RoomType();
		rmTyp.setRoomTypeCode("roomCode");
		Map<String, com.mmt.hotels.model.response.pricing.RatePlan> ratePlanList = new HashMap<>();
		com.mmt.hotels.model.response.pricing.RatePlan ratePln = new com.mmt.hotels.model.response.pricing.RatePlan();
		ratePln.setRatePlanCode("rtPln1");
		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		List<CancellationPolicyTimeline> cancellationPolicyTimelines = new ArrayList<>();
		cancellationPolicyTimelines.add(new CancellationPolicyTimeline());
		cancellationTimeline.setCancellationPolicyTimelineList(cancellationPolicyTimelines);
		ratePln.setCancellationTimeline(cancellationTimeline);
		ratePlanList.put("rtPln1", ratePln);
		rmTyp.setRatePlanList(ratePlanList);
		roomTypes.add(rmTyp);
		BNPLDetails bnplDetails = new BNPLDetails();
		List<RatePlan> resp = commonResponseTransformer.buildRateplanList(roomTypes, bnplDetails, false, null, false, false);
		Assert.assertNotNull(resp);
	}

	@Test
	public void getDoubleBlackInfoTest() {
		Assert.assertNotNull(commonResponseTransformer.getDoubleBlackInfo(new DoubleBlackValidateResponse()));
	}

	@Test
	public void getPriceMapTest() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setCouponInfo(new BestCoupon());
		displayPriceBreakDown.getCouponInfo().setDiscountAmount(0d);
		Emi emiDetails = new Emi();
		emiDetails.setEmiAmount(234.2f);
		emiDetails.setInterestRate(1.2f);
		displayPriceBreakDown.setEmiDetails(emiDetails);
		List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
		displayPriceBreakDownList.add(displayPriceBreakDown);
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.DETAIL_SEARCH_ROOMS);
		MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), Constants.B2C);
		displayPriceBreakDown.setTotalTax(9.0d);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);
		boolean isAltAccoHotel = false;
		MarkUpDetails markUpDetails=new MarkUpDetails();
		markUpDetails.setMarkupEligible(true);
		markUpDetails.setMarkupMap(new HashMap<>());
		markUpDetails.getMarkupMap().put("DH", new MarkUp());
		markUpDetails.getMarkupMap().get("DH").setValue(10.2);
		markUpDetails.getMarkupMap().get("DH").setType(MarkUpType.PERCENTAGE);
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T}", 2, "INR",null, 1,  true, "1152",true, false, true,true,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{IWD:T}", 2, "INR",null, 1,  true, "1152",true, false, true,true,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T,PDO:PNT}", 2, "INR",null, 2,  true, "1152",true, true, true,true,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T,PDO:PRNT}", 1, "INR","bed", 1,  true, "1152",true, true, true,false,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T,PDO:PN}", 1, "INR",null, 2,  true, "1152",true, true, false,false,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T,PDO:TP}", 2, "INR",null, 1,  true, "1152",true, true, true,false,isAltAccoHotel, markUpDetails, null, null, false, false));
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T,PDO:TPT}", 2, "INR",null, 3,  true, "1152",true, true, true,false,isAltAccoHotel, markUpDetails, null, null, false, false));

		displayPriceBreakDown.setCouponInfo(null);
		Assert.assertNotNull(commonResponseTransformer.getPriceMap(displayPriceBreakDown, displayPriceBreakDownList, "{ST:T}", 2, "INR",null, 1,  true, "1152",true, true, true,false,isAltAccoHotel, null, null, null, false, false));
	}

	@Test
	public void getPriceDisplayMessageTest(){
		when(polyglotService.getTranslatedData("BED_SELLABLE_TYPE")).thenReturn("Bed");
		when(polyglotService.getTranslatedData("ROOM_SELLABLE_TYPE")).thenReturn("Room");
		when(polyglotService.getTranslatedData("PER_NIGHT_TEXT")).thenReturn("Per Night");
		when(polyglotService.getTranslatedData("PER_ROOM_PER_NIGHT")).thenReturn("Per {room} per night");
		when(polyglotService.getTranslatedData("GROUP_PER_ROOM_PER_NIGHT")).thenReturn("Per Room/Night");
		when(polyglotService.getTranslatedData("PER_NIGHT_FOR_NUM_ROOMS")).thenReturn("Per night for {num} {rooms}s");
		when(polyglotService.getTranslatedData("PER_NIGHT_WITH_TAX")).thenReturn("Per Night with Tax");
		when(polyglotService.getTranslatedData("FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX")).thenReturn("For {num} Rooms Per Night with Tax");
		when(polyglotService.getTranslatedData("PER_NIGHT_FOR_NUM_ROOMS_TEXT")).thenReturn("Per Night for {num} Rooms");
		when(polyglotService.getTranslatedData("FOR_NUM_ROOMS_PER_NIGHT_WITH_TAX")).thenReturn("For {num} Rooms Per Night with Tax");
		when(polyglotService.getTranslatedData("TOTAL_PRICE_TEXT")).thenReturn("Total Price");
		when(polyglotService.getTranslatedData("FOR_NUM_NIGHTS")).thenReturn("For {num} Nights");
		when(polyglotService.getTranslatedData("FOR_NUM_NIGHTS_NUM_ROOMS")).thenReturn("For {num_nights} Nights, {num_rooms} Rooms");
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRN}", 2, "bed", 2, false,true, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRNT}", 2, "bed", 2, false,true, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRN}", 2, "bed", 2, true,true, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRNT}", 2, "bed", 2, true,true, false));
		Assert.assertEquals("Per Room per night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRNT}", 2, "room", 2, false,false, false));
		Assert.assertEquals("Per Room/Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PRNT}", 2, "room", 2, true,false, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PN}", 1, "bed", 2, false,true, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PN}", 2, "bed", 2, false,true, false));
		Assert.assertEquals("Per night for 2 Rooms",commonResponseTransformer.getPriceDisplayMessage("{PDO:PN}", 2, "room", 2, false,false, false));
		Assert.assertEquals("Per Night with Tax",commonResponseTransformer.getPriceDisplayMessage("{PDO:PNT}", 2, "room", 2, false,true, false));
		Assert.assertEquals("Per Night",commonResponseTransformer.getPriceDisplayMessage("{PDO:PNT}", 1, "room", 1, false,false, false));
		Assert.assertEquals("For 2 Rooms Per Night with Tax",commonResponseTransformer.getPriceDisplayMessage("{PDO:PNT}", 2, "room", 2, false,false, false));
		Assert.assertEquals("Per Night for 2 Rooms",commonResponseTransformer.getPriceDisplayMessage("{PDO:PNT}", 2, "room", 1, false,false, false));
		Assert.assertEquals("For 2 Rooms Per Night with Tax",commonResponseTransformer.getPriceDisplayMessage("{PDO:PNT}", 2, "room", 2, false,false, false));
		Assert.assertEquals("Total Price", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 2, "room", null, false, true, false));
		Assert.assertEquals("Per Night", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 1, "room", 1, false, true, false));
		Assert.assertEquals("For 2 Nights", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 1, "room", 2, false, true, false));
		Assert.assertEquals("Per Night", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 2, "room", 1, false, true, false));
		Assert.assertEquals("Per Night for 2 Rooms", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 2, "room", 1, false, false, false));
		Assert.assertEquals("For 2 Nights", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 2, "room", 2, false, true, false));
		Assert.assertEquals("For 2 Nights, 2 Rooms", commonResponseTransformer.getPriceDisplayMessage("{PDO:TP}", 2, "room", 2, false, false, false));
		Assert.assertEquals("Total Price", commonResponseTransformer.getPriceDisplayMessage("{PDO:TPT}", 2, "room", 2, false, false, false));
	}

	@Test
	public void buildInsuranceTest() {
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setInsuranceBreakupMap(new HashMap<>());
		displayPriceBreakDown.getInsuranceBreakupMap().put("234", new InsuranceDetails());
		displayPriceBreakDown.getInsuranceBreakupMap().get("234").setAmount(20.0d);
		displayPriceBreakDown.getInsuranceBreakupMap().get("234").setDisplayLabel("Reliane");
		commonResponseTransformer.buildInsuranceBreakup(pricingDetails, displayPriceBreakDown, "insurance");
		Assert.assertNotNull(pricingDetails);
	}

	@Test
	public void buildCharityAddonBreakUpTest(){
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setInsuranceBreakupMap(new HashMap<>());
		displayPriceBreakDown.getInsuranceBreakupMap().put("234", new InsuranceDetails());
		displayPriceBreakDown.getInsuranceBreakupMap().get("234").setAmount(20.0d);
		displayPriceBreakDown.getInsuranceBreakupMap().get("234").setDisplayLabel("Reliane");
		commonResponseTransformer.buildCharityAddonBreakUp(pricingDetails, displayPriceBreakDown);
		Assert.assertEquals(0,pricingDetails.size());

		displayPriceBreakDown.setCharityV2Enable(true);
		displayPriceBreakDown.setCharityAmountV2(10);
		commonResponseTransformer.buildCharityAddonBreakUp(pricingDetails, displayPriceBreakDown);
		Assert.assertEquals(1,pricingDetails.size());
	}

	@Test
	public void buildSelectedSpecialRequestTest() {
		SpecialRequest specialRequest = new SpecialRequest();
		specialRequest.setCategories(new ArrayList<>());
		specialRequest.getCategories().add(new SpecialRequestCategory());
		specialRequest.getCategories().get(0).setCode("test");
		Assert.assertNotNull(commonResponseTransformer.buildSelctedSpecialRequests(specialRequest, specialRequest));
		specialRequest.getCategories().get(0).setCode("109");
		String[] str = {"e1", "e2", "e4"};
		specialRequest.getCategories().get(0).setValues(str);
		Assert.assertNotNull(commonResponseTransformer.buildSelctedSpecialRequests(specialRequest, specialRequest));
	}

	@Test
	public void buildPropertyRulesTest() {
		RequestInputBO inputBo = new RequestInputBO.Builder().buildNotices(new ArrayList<>()).
				buildPahWithCC(true).buildCancellationPolicyType("NR").
				buildHouseRules(new HouseRules()).buildMustReadRules(new ArrayList<>())
				.buildCheckinPolicy(new RatePolicy()).buildConfirmationPolicy(new RatePolicy()).build();
		inputBo.getConfirmationPolicy().setDescription("test");
		inputBo.getCheckinPolicy().setDescription("test");
		inputBo.getNotices().add(new Notices());
		inputBo.getNotices().get(0).setDescription("test");
		List<Policy> policyList = new ArrayList<>();
		policyList.add(new Policy());
		policyList.get(0).setRules(new ArrayList<>());
		policyList.get(0).getRules().add("test1");
		inputBo.getMustReadRules().add("test");
		when(policiesResponseTransformer.buildHouseRules(Mockito.any())).thenReturn(policyList);
		Assert.assertNotNull(commonResponseTransformer.buildPropertyRules(inputBo));
	}

	@Test
	public void buildAdditionalChargesTest() {
		//cityCode is set as CTMALDI to test for Transfers Message
		AdditionalChargesBO inputBo = new AdditionalChargesBO.Builder().buildHotelierCurrency("USD").buildAdditionalFees(new ArrayList<>()).buildCityCode(Constants.CITY_CODE_MALDIVES).buildPropertyType("test").build();
		Assert.assertNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null, null));
		inputBo.getAdditionalFees().add(new AdditionalFees());
		inputBo.getAdditionalFees().get(0).setAmount(0d);
		inputBo.getAdditionalFees().get(0).setPrice(new AdditionalFeesPrice());
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayRoom(1d);
		inputBo.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayChild(1d);
		inputBo.getAdditionalFees().get(0).setTotalChild(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null,null));
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayRoom(null);
		inputBo.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null,null));
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(null);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightRoom(1d);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null,null));
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightRoom(null);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightAdult(1d);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null,null));
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightAdult(1d);
		inputBo.getAdditionalFees().get(0).getPrice().setPerNightChild(1d);
		inputBo.getAdditionalFees().get(0).setTotalChild(1);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, null,null));
		inputBo.getAdditionalFees().get(0).setLeafCategory(Constants.TRANSFERS);
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, true, null, null,null).getBreakup().get(0).getChargesMsg());
		inputBo.getAdditionalFees().add(new AdditionalFees());
		inputBo.getAdditionalFees().get(1).setName("an");
		inputBo.getAdditionalFees().get(1).setDescription("ad");
		inputBo.getAdditionalFees().get(1).setAmount(0d);
		inputBo.getAdditionalFees().get(1).setPrice(new AdditionalFeesPrice());
		inputBo.getAdditionalFees().get(1).getPrice().setPerStayRoom(1d);
		inputBo.getAdditionalFees().get(1).setTotalRooms(1);
		inputBo.getAdditionalFees().get(1).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(1).setTotalAdults(1);
		inputBo.getAdditionalFees().get(1).getPrice().setPerStayChild(1d);
		inputBo.getAdditionalFees().get(1).setTotalChild(1);
		inputBo.getAdditionalFees().get(1).setCurrency("INR");
		inputBo.getAdditionalFees().get(1).setPropertyType(Collections.singletonList("Transfers"));
		Assert.assertNotNull(commonResponseTransformer.buildAdditionalCharges(inputBo, true, null, null,null).getBreakup().get(0).getChargesMsg());
		Assert.assertEquals("mandatory_charges_section_desc", commonResponseTransformer.buildAdditionalCharges(inputBo, true, null, null,null).getSubTitle());
		AdditionalChargesBO inputB1 = new AdditionalChargesBO.Builder().buildHotelierCurrency("INR").buildAdditionalFees(new ArrayList<>()).buildCityCode(Constants.CITY_CODE_MALDIVES).buildPropertyType("test").build();
		inputB1.getAdditionalFees().add(new AdditionalFees());
		inputB1.getAdditionalFees().get(0).setName("an");
		inputB1.getAdditionalFees().get(0).setDescription("ad");
		inputB1.getAdditionalFees().get(0).setAmount(0d);
		inputB1.getAdditionalFees().get(0).setPrice(new AdditionalFeesPrice());
		inputB1.getAdditionalFees().get(0).getPrice().setPerStayRoom(1d);
		inputB1.getAdditionalFees().get(0).setTotalRooms(1);
		inputB1.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputB1.getAdditionalFees().get(0).setTotalAdults(1);
		inputB1.getAdditionalFees().get(0).getPrice().setPerStayChild(1d);
		inputB1.getAdditionalFees().get(0).setTotalChild(1);
		inputB1.getAdditionalFees().get(0).setCurrency("INR");
		inputB1.getAdditionalFees().get(0).setPropertyType(Collections.singletonList("Transfers"));
		AdditionalMandatoryCharges additionalMandatoryCharges = commonResponseTransformer.buildAdditionalCharges(inputB1, true, null, null,null);
		Assert.assertEquals("MANDATORY_CHARGES_SECTION_DESC_DH", additionalMandatoryCharges.getSubTitle());

	}

	@Test
	public void testAdditionalFeeCategoryDesc() {
		// Prepare AdditionalChargesInputBo that contains additional fees with leaf category transfers.
		AdditionalChargesBO inputBo = new AdditionalChargesBO.Builder()
				.buildHotelierCurrency("USD")
				.buildAdditionalFees(new ArrayList<>())
				.buildCityCode(Constants.CITY_CODE_MALDIVES)
				.buildPropertyType("test")
				.buildRoomName("Deluxe Room")
				.buildCityName("Maldives")
				.build();
		inputBo.getAdditionalFees().add(new AdditionalFees());
		inputBo.getAdditionalFees().get(0).setName("Property Tax");
		inputBo.getAdditionalFees().get(0).setDescription("Property Tax");
		inputBo.getAdditionalFees().get(0).setAmount(20d);
		inputBo.getAdditionalFees().get(0).setCurrency("INR");
		inputBo.getAdditionalFees().get(0).setLeafCategory("TRANSFERS");
		inputBo.getAdditionalFees().get(0).setPropertySubType(Collections.singletonList("DROP_OFF"));
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(EXTRA_BNB_EXP_KEY, "true");
		AdditionalMandatoryCharges resp = commonResponseTransformer.buildAdditionalCharges(
				inputBo, true, null, null, expDataMap);
		Assert.assertEquals("Included drop-off charges", resp.getBreakup().get(0).getCategoryDesc());

		// Checking Gala meals text.
		inputBo.getAdditionalFees().get(0).setLeafCategory(GALA_MEALS);
		resp = commonResponseTransformer.buildAdditionalCharges(
				inputBo, true, null, null, expDataMap);
		Assert.assertNotNull(resp.getBreakup().get(0).getCategoryDesc());
	}

	@Test
	public void buildBlackInfoTest() {
		Assert.assertNotNull(commonResponseTransformer.buildBlackInfo(new BlackInfo()));
		Assert.assertNull(commonResponseTransformer.buildBlackInfo(null));
	}

	@Test
	public void buildTagsTest() {
		Assert.assertNull(commonResponseTransformer.buildTags(new ArrayList<>()));
		List<CorpTags> tagList = new ArrayList<>();
		tagList.add(new CorpTags());
		Assert.assertNotNull(commonResponseTransformer.buildTags(tagList));
	}

	@Test
	public void getImportantInfoSectionTest() {
		RequestInputBO inputBo = new RequestInputBO.Builder().build();
		Assert.assertNotNull(commonResponseTransformer.getImportantInfoSection(inputBo));
	}

	@Test
	public void testNavigationRules() {
		RequestInputBO.Builder inputBO = new RequestInputBO.Builder();
		// Build deposit policy in the inputBo
		DepositPolicy depositPolicy = new DepositPolicy();
		depositPolicy.setId("DEPOSIT_CHARGES");
		depositPolicy.setCategoryName("Deposit Charges");
		depositPolicy.setRuleInfo("Deposit Charges");
		depositPolicy.setRuleTableInfo(new RuleTableInfo());
		depositPolicy.getRuleTableInfo().setInfoList(new ArrayList<>());
		depositPolicy.getRuleTableInfo().getInfoList().add(new RuleInfo());
		depositPolicy.getRuleTableInfo().getInfoList().get(0).setKey("K1");
		depositPolicy.getRuleTableInfo().getInfoList().get(0).setValue(Arrays.asList("V1", "V2"));
		inputBO.buildDepositPolicy(depositPolicy);
        assertFalse(commonResponseTransformer.getImportantInfoSection(inputBO.build())
                .getNavigationRules().isEmpty());
	}

	@Test
	public void testCategoryInfoRulesWithoutRuleTableInfo() {
		RequestInputBO.Builder inputBO = new RequestInputBO.Builder();
		CategoryInfo categoryInfo = new CategoryInfo();
		categoryInfo.setId("BREAKFAST_CHARGES");
		categoryInfo.setCategoryName("Breakfast Charges");
		categoryInfo.setRuleInfo("Breakfast Charges");

		CategoryInfo categoryInfoEB = new CategoryInfo();
		categoryInfoEB.setId("EXTRA_BED_POLICY");
		categoryInfoEB.setCategoryName("Extra Bed Policy");
		categoryInfoEB.setRuleInfo("Extra Bed Policy");

		HouseRules houseRules = new HouseRules();
		houseRules.setCategoryInfoList(Arrays.asList(categoryInfo, categoryInfoEB));
		inputBO.buildHouseRules(houseRules);
		assertTrue(commonResponseTransformer.getImportantInfoSection(inputBO.build())
				.getNavigationRules().isEmpty());
	}

	@Test
	public void testCategoryInfoRulesWithNullValuesInRuleTableInfo() {
		RequestInputBO.Builder inputBO = new RequestInputBO.Builder();
		CategoryInfo categoryInfo = new CategoryInfo();
		categoryInfo.setId("BREAKFAST_CHARGES");
		categoryInfo.setCategoryName("Breakfast Charges");
		categoryInfo.setRuleInfo("Breakfast Charges");
		categoryInfo.setRuleTableInfo(new RuleTableInfo());
		categoryInfo.getRuleTableInfo().setInfoList(new ArrayList<>());
		categoryInfo.getRuleTableInfo().getInfoList().add(new RuleInfo());
		categoryInfo.getRuleTableInfo().getInfoList().get(0).setKey("null");
		categoryInfo.getRuleTableInfo().getInfoList().get(0).setValue(Arrays.asList("V1", "V2"));

		CategoryInfo categoryInfoEB = new CategoryInfo();
		categoryInfoEB.setId("EXTRA_BED_POLICY");
		categoryInfoEB.setCategoryName("Extra Bed Policy");
		categoryInfoEB.setRuleInfo("Extra Bed Policy");
		categoryInfoEB.setRuleTableInfo(new RuleTableInfo());
		categoryInfoEB.getRuleTableInfo().setInfoList(new ArrayList<>());
		categoryInfoEB.getRuleTableInfo().getInfoList().add(new RuleInfo());
		categoryInfoEB.getRuleTableInfo().getInfoList().get(0).setKey("K1");
		categoryInfoEB.getRuleTableInfo().getInfoList().get(0).setValue(new ArrayList<>());

		HouseRules houseRules = new HouseRules();
		houseRules.setCategoryInfoList(Arrays.asList(categoryInfo, categoryInfoEB));
		inputBO.buildHouseRules(houseRules);
		assertTrue(commonResponseTransformer.getImportantInfoSection(inputBO.build())
				.getNavigationRules().isEmpty());
	}

	@Test
	public void testCategoryInfoRules() {
		RequestInputBO.Builder inputBO = new RequestInputBO.Builder();
		CategoryInfo categoryInfo = new CategoryInfo();
		categoryInfo.setId("BREAKFAST_CHARGES");
		categoryInfo.setCategoryName("Breakfast Charges");
		categoryInfo.setRuleInfo("Breakfast Charges");
		categoryInfo.setRuleTableInfo(new RuleTableInfo());
		categoryInfo.getRuleTableInfo().setInfoList(new ArrayList<>());
		categoryInfo.getRuleTableInfo().getInfoList().add(new RuleInfo());
		categoryInfo.getRuleTableInfo().getInfoList().get(0).setKey("K1");
		categoryInfo.getRuleTableInfo().getInfoList().get(0).setValue(Arrays.asList("V1", "V2"));

		CategoryInfo categoryInfoEB = new CategoryInfo();
		categoryInfoEB.setId("EXTRA_BED_POLICY");
		categoryInfoEB.setCategoryName("Extra Bed Policy");
		categoryInfoEB.setRuleInfo("Extra Bed Policy");
		categoryInfoEB.setRuleTableInfo(new RuleTableInfo());
		categoryInfoEB.getRuleTableInfo().setInfoList(new ArrayList<>());
		categoryInfoEB.getRuleTableInfo().getInfoList().add(new RuleInfo());
		categoryInfoEB.getRuleTableInfo().getInfoList().get(0).setKey("K1");
		categoryInfoEB.getRuleTableInfo().getInfoList().get(0).setValue(Arrays.asList("V1", "V2"));

		HouseRules houseRules = new HouseRules();
		houseRules.setCategoryInfoList(Arrays.asList(categoryInfo, categoryInfoEB));
		inputBO.buildHouseRules(houseRules);
		assertFalse(commonResponseTransformer.getImportantInfoSection(inputBO.build())
				.getNavigationRules().isEmpty());
	}

	@Test
	public void testGetAmenities() {
		List<FacilityGroup> amenities = new ArrayList<>();
		FacilityGroup fcltyGrp = new FacilityGroup();
		List<Facility> facilities = new ArrayList<>();
		Facility fclty = new Facility();
		fclty.setDisplayType("2");
		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility attr = new AttributesFacility();
		childAttributes.add(attr );
		fclty.setChildAttributes(childAttributes );
		facilities.add(fclty );
		fcltyGrp.setFacilities(facilities );
		amenities.add(fcltyGrp );
		List<SelectRoomAmenities> amnts = commonResponseTransformer.getAmenities(amenities, true);
		Assert.assertNotNull(amnts);
	}

	@Test
	public void testGetHotelCategories_hotelCategoryIsEmpty_returnsNull(){
		Assert.assertNull(commonResponseTransformer.getHotelCategories(null,false, false));
	}

	@Test
	public void testGetHotelCategories_hotelCategoryIsNotEmpty(){
		Set<String> hotelCategories = new HashSet<>();
		hotelCategories.add("");
		hotelCategories.add("category");
		List<String> hotelCategoryTypesPriority = new ArrayList<>();
		hotelCategoryTypesPriority.add("");
		ReflectionTestUtils.setField(commonResponseTransformer,"categoryTextToCategoryTypeMap",new HashMap<>());
		ReflectionTestUtils.setField(commonResponseTransformer,"hotelCategoryTypesPriority",hotelCategoryTypesPriority);
		Assert.assertNotEquals(hotelCategoryTypesPriority.subList(0,1),commonResponseTransformer.getHotelCategories(hotelCategories,false, false));
	}

	@Test
	public void testBuildAmountYouPayingNow(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAmountYouPayingNow", new ArrayList<PricingDetails>(),null, "");
	}

	@Test
	public void buildBaseFare_buildTaxesAndServiceFee_buildWallet_buildPriceAfterDiscount_buildTotalDiscounts(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(1);
		displayPriceBreakDown.setHotelTax(1);
		displayPriceBreakDown.setHotelServiceCharge(1);
		displayPriceBreakDown.setMmtServiceCharge(1);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(1);
		displayPriceBreakDown.setMmtDiscount(4);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL, 1.0d);
		displayPriceBreakDown.setCdfDiscount(2);
		displayPriceBreakDown.setFlexiCancellationCharges(1d);
		displayPriceBreakDown.setLosBenefitsDiscount(1d);
		List<PricingDetails> pricingDetails = new ArrayList<PricingDetails>();
		String expData = "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildBaseFare", new ArrayList<PricingDetails>(), displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTaxesAndServiceFee", pricingDetails, displayPriceBreakDown, "IN", expData, true);
		Assert.assertNull(pricingDetails.get(0).getBreakup());
		pricingDetails = new ArrayList<PricingDetails>();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTaxesAndServiceFee", pricingDetails, displayPriceBreakDown, "IN", expData, false);
		Assert.assertNotNull(pricingDetails.get(0).getBreakup());
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildWallet", new ArrayList<PricingDetails>(), displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildPriceAfterDiscount", new ArrayList<PricingDetails>(), displayPriceBreakDown);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTotalDiscounts", new ArrayList<PricingDetails>(), displayPriceBreakDown, true, "1152", "LSOF:T","FIRST_FIVE_BOOKING",false, false);
	}

	@Test
	public void test_buildDiscount(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(20);
		displayPriceBreakDown.setHotelTax(1);
		displayPriceBreakDown.setHotelServiceCharge(1);
		displayPriceBreakDown.setMmtServiceCharge(1);
		displayPriceBreakDown.setAffiliateFee(1);
		displayPriceBreakDown.setWallet(1);
		displayPriceBreakDown.setSavingPerc(2.0);
		displayPriceBreakDown.setMmtDiscount(4);
		displayPriceBreakDown.setCdfDiscount(5);
		displayPriceBreakDown.setBlackDiscount(1);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.LOS_DEAL,1.0d);

		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);
		List<PricingDetails> pricingDetails = new ArrayList<PricingDetails>();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTotalDiscounts", pricingDetails,displayPriceBreakDown, true, "1152","{LSOF:T}","FIRST_FIVE_BOOKING",false, false);
		Assert.assertNotNull(pricingDetails);
	}

	@Test
	public void testtBuildCancellationTimeline(){
		Assert.assertNotEquals(new CancellationTimeline(),commonResponseTransformer.buildCancellationTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline(), null));
		Assert.assertNotEquals(new CancellationTimeline(),commonResponseTransformer.buildCancellationTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline(), BNPLVariant.BNPL_AT_0));
		Assert.assertNotEquals(new CancellationTimeline(),commonResponseTransformer.buildCancellationTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline(), BNPLVariant.BNPL_AT_1));
	}

	@Test
	public void testtBuildCancellationPolicyTimeline(){
		Assert.assertNotEquals(new CancellationPolicyTimeline(),commonResponseTransformer.buildCancellationPolicyTimeline(new com.mmt.hotels.model.response.pricing.CancellationTimeline(), false, null));
	}

	@Test
	public void buildAffiliateFeeDetails_affiliateFeeDetailsIsEmpty_returnsNull(){
		Assert.assertNull(commonResponseTransformer.buildAffiliateFeeDetails(null));
	}

	@Test
	public void buildAffiliateFeeDetails_affiliateFeeDetailsIsNotEmpty_returnsAffiliateFeeDetailsList(){
		List<AffiliateFeeDetails> affiliateFeeDetailsList = new ArrayList<>();
		affiliateFeeDetailsList.add(new  AffiliateFeeDetails());
		Assert.assertNotNull(commonResponseTransformer.buildAffiliateFeeDetails(affiliateFeeDetailsList));
	}

	// ==================== Start of buildMealDetails Test Methods ====================

	@Test
	public void testBuildMealDetails_withNullFoodDiningRule_shouldReturn() {
		// Arrange
		FoodDiningRule foodDiningRule = null;
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert - method should return early without throwing exception
		// No assertions needed as method returns void and should handle null gracefully
	}

	@Test
	public void testBuildMealDetails_withNullMealDetailsHES_shouldReturn() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = null;

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert - method should return early without throwing exception
		// No assertions needed as method returns void and should handle null gracefully
	}

	@Test
	public void testBuildMealDetails_withBothParametersNull_shouldReturn() {
		// Arrange
		FoodDiningRule foodDiningRule = null;
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = null;

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert - method should return early without throwing exception
		// No assertions needed as method returns void and should handle null gracefully
	}

	@Test
	public void testBuildMealDetails_withEmptyMealDetailsHES_shouldSetDefaultHeading() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE)).thenReturn("Multiple Options Available");

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert
		assertEquals("Multiple Options Available", foodDiningRule.getHeading());
		assertNull(foodDiningRule.getMealOptionsInfo());
		assertNull(foodDiningRule.getNote());
		assertNull(foodDiningRule.getRules());
		assertNull(foodDiningRule.getMealOptions());
	}

	@Test
	public void testBuildMealDetails_withMultipleMealOptions_shouldSetMultiOptionHeading() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		List<com.mmt.hotels.model.response.staticdata.meals.MealOption> mealOptions = new ArrayList<>();
		com.mmt.hotels.model.response.staticdata.meals.MealOption option1 = new com.mmt.hotels.model.response.staticdata.meals.MealOption();
		option1.setOptionType("FIXED_MENU");
		com.mmt.hotels.model.response.staticdata.meals.MealOption option2 = new com.mmt.hotels.model.response.staticdata.meals.MealOption();
		option2.setOptionType("COOK");
		mealOptions.add(option1);
		mealOptions.add(option2);
		mealDetailsHES.setMealOptions(mealOptions);

		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE)).thenReturn("Multiple Options Available");
		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_INFO_TEXT)).thenReturn("Choose from multiple dining options");

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert
		assertEquals("Multiple Options Available", foodDiningRule.getHeading());
		assertEquals("Choose from multiple dining options", foodDiningRule.getMealOptionsInfo());
		assertNotNull(foodDiningRule.getMealOptions());
		assertEquals(2, foodDiningRule.getMealOptions().size());
	}

	@Test
	public void testBuildMealDetails_withSingleFixedMenuOption_shouldSetFixedMenuHeading() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		List<com.mmt.hotels.model.response.staticdata.meals.MealOption> mealOptions = new ArrayList<>();
		com.mmt.hotels.model.response.staticdata.meals.MealOption option = new com.mmt.hotels.model.response.staticdata.meals.MealOption();
		option.setOptionType(Constants.MEAL_OPTION_TYPE_FIXED_MENU);
		mealOptions.add(option);
		mealDetailsHES.setMealOptions(mealOptions);

		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FIXED_MENU_TITLE)).thenReturn("Fixed Menu Available");

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert
		assertEquals("Fixed Menu Available", foodDiningRule.getHeading());
		assertNull(foodDiningRule.getMealOptionsInfo());
		assertNotNull(foodDiningRule.getMealOptions());
		assertEquals(1, foodDiningRule.getMealOptions().size());
	}

	@Test
	public void testBuildMealDetails_withSingleCookOption_shouldSetCookHeading() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		List<com.mmt.hotels.model.response.staticdata.meals.MealOption> mealOptions = new ArrayList<>();
		com.mmt.hotels.model.response.staticdata.meals.MealOption option = new com.mmt.hotels.model.response.staticdata.meals.MealOption();
		option.setOptionType(Constants.MEAL_OPTION_TYPE_COOK);
		mealOptions.add(option);
		mealDetailsHES.setMealOptions(mealOptions);

		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_COOK_OPTION_TITLE)).thenReturn("Cooking Facilities Available");

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert
		assertEquals("Cooking Facilities Available", foodDiningRule.getHeading());
		assertNull(foodDiningRule.getMealOptionsInfo());
		assertNull(foodDiningRule.getMealOptions()); // Should be null for single COOK option
	}

	@Test
	public void testBuildMealDetails_withSingleUnknownOptionType_shouldSetDefaultHeading() {
		// Arrange
		FoodDiningRule foodDiningRule = new FoodDiningRule();
		com.mmt.hotels.model.response.staticdata.meals.MealClarity mealDetailsHES = new com.mmt.hotels.model.response.staticdata.meals.MealClarity();

		List<com.mmt.hotels.model.response.staticdata.meals.MealOption> mealOptions = new ArrayList<>();
		com.mmt.hotels.model.response.staticdata.meals.MealOption option = new com.mmt.hotels.model.response.staticdata.meals.MealOption();
		option.setOptionType("UNKNOWN_TYPE");
		mealOptions.add(option);
		mealDetailsHES.setMealOptions(mealOptions);

		when(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_MULTI_OPTION_TITLE)).thenReturn("Multiple Options Available");

		// Act
		commonResponseTransformer.buildMealDetails(foodDiningRule, mealDetailsHES);

		// Assert
		assertEquals("Multiple Options Available", foodDiningRule.getHeading());
		assertNull(foodDiningRule.getMealOptionsInfo());
		assertNotNull(foodDiningRule.getMealOptions());
		assertEquals(1, foodDiningRule.getMealOptions().size());
	}

	@Test
	public void testGetHighlightedAmenities() {
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		facilityGroup.setName("");
		facilityGroup.setFacilities(new ArrayList<>());
		facilityGroup.getFacilities().add(new Facility());
		facilityGroup.getFacilities().get(0).setName("abc");
		highlightedAmenities.add(facilityGroup);
		Assert.assertNotNull(commonResponseTransformer.getHighlightedAmenities(highlightedAmenities));
	}

	@Test
	public void buildListPersonalizationResponse_listPersonalizationResponseIsNull_returnsNull() {
		Assert.assertNull(commonResponseTransformer.buildListPersonalizationResponse(null,"",new LinkedHashMap<>()));
	}
	@Test
	public void buildListPersonalizationResponse_listPersonalizationResponseIsNotNull_returnsNotNull() {
		ListPersonalizationResponse listPersonalizationResponse = new ListPersonalizationResponse();
		listPersonalizationResponse.setCardData(new HashMap<>());
		listPersonalizationResponse.getCardData().put(1, new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).add(new CardData());
		listPersonalizationResponse.getCardData().get(1).get(0).setCardPayload(new CardPayloadResponse());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardPayload().setAltAccoDiscovery(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardPayload().setAltAccoData(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).setCardAction(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().add(new CardAction());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).setPriceBucket(new PriceBucket());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).setMatchmakerTags(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getMatchmakerTags().add(new MatchmakerTag());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).setData(new CardActionData());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getData().setSections(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getData().getSections().add(new Section());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getData().getSections().get(0).setItems(new ArrayList<>());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getData().getSections().get(0).getItems().add(new Item());
		listPersonalizationResponse.getCardData().get(1).get(0).getCardAction().get(0).getData().getSections().get(0).getItems().get(0).setText("test");

		Assert.assertNotNull(commonResponseTransformer.buildListPersonalizationResponse(listPersonalizationResponse,"ANDROID",new LinkedHashMap<>()));
	}

	@Test
	public void buildCardInfo(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardInfo", null, "",null);
		when(cardDataCBList.get(0)).thenReturn(cardDataCB);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardInfo", cardDataCBList, "",null);
	}

	@Test
	public void buildCardCondition(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardCondition", cardCondition);
	}

	@Test
	public void buildFilters(){
		Set<Filter> filters = new HashSet<>();
		filters.add(filter);
		when(filter.getFilterGroup()).thenReturn(STAR_RATING);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFilters", filters);
	}

	@Test
	public void buildCardPayload_and_buildGenericCardData(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardPayload", cardPayload,"",null);
		List<GenericCardPayloadData> genericCardData = new ArrayList<>();
		genericCardData.add(genericCardPayloadData);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildGenericCardData", genericCardData);
	}

	@Test
	public void buildSpokeCityDataEmptyTest(){
		List<SpokeCity> spokeCityList = new ArrayList<>();
		List<SpokeCityCG> spokeCityCGList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSpokeCityData", spokeCityList);
		Assert.assertEquals(0,spokeCityCGList.size());
	}

	@Test
	public void buildSpokeCityDataTest(){
		List<SpokeCity> spokeCityList = new ArrayList<>();
		SpokeCity spokeCity = new SpokeCity();
		spokeCity.setLocId("CITY");
		spokeCityList.add(spokeCity);
		List<SpokeCityCG> spokeCityCGList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSpokeCityData", spokeCityList);
		Assert.assertEquals(1,spokeCityCGList.size());
	}

	@Test
	public void buildHotelListNewEmptyTest(){
		CardPayloadData cardPayloadData =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardPayload", cardPayload,"",null);
		List<Hotel> hotelList =  ReflectionTestUtils.invokeMethod(commonResponseTransformer, "convertToHotelListNew", cardPayloadData);
		Assert.assertNull(hotelList);
	}


	@Test
	public void buildAltAccoDiscovery(){
		List<CollectionsResponseBo<SearchWrapperHotelEntityAbridged>> list = new ArrayList<>();
		list.add(searchWrapperHotelEntity);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAltAccoDiscovery", null,"");
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAltAccoDiscovery", list,"");
	}

	@Test
	public void buildCollectionsResponse(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCollectionsResponse", null,"");
	}

	@Test
	public void buildCardAction(){
		List<com.mmt.hotels.pojo.listing.personalization.CardAction> list = new ArrayList<>();
		list.add(cardAction);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardAction", list);
	}

	@Test
	public void buildContextualFilterData(){
		List<com.mmt.hotels.model.response.listpersonalization.ContextualFilterData> list = new ArrayList<>();
		list.add(contextualFilterData);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildContextualFilterData", list);
	}

	@Test
	public void buildPropertyPersuasions(){
		List<PropertyPersuasions> list = new ArrayList<>();
		list.add(propertyPersuasions);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildPropertyPersuations", list);
	}

	@Test
	public void buildSearchContext(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildSearchContext", searchContext);
	}

	@Test
	public void getConfirmationPolicy(){
		Assert.assertNull(commonResponseTransformer.getConfirmationPolicy(roomTypeDetails));
	}

	@Test
	public void buildFilterRange(){
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFilterRange", filterRange);
	}

	@Test
	public void buildAppliedFilterMap(){
		Set<Filter> filters= new HashSet<>();
		filters.add(filter);
		when(filter.getFilterGroup()).thenReturn(STAR_RATING);
		Map<FilterGroup, Set<Filter>> appliedFilterMap = new HashMap<>();
		appliedFilterMap.put(STAR_RATING,filters);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildAppliedFilterMap", appliedFilterMap);
	}

	@Test
	public void buildPolarisDataTest() {
		PolarisData polarisDataCB = new PolarisData();
		polarisDataCB.setShowImage(true);
		List<PolarisTag> tagListCB = new ArrayList<>();
		PolarisTag polarisTagCB = new PolarisTag();
		polarisTagCB.setId("1");
		polarisTagCB.setDesc("testDescription");
		polarisTagCB.setBbox(new BbLatLong());
		polarisTagCB.getBbox().setNe(new LatLong());
		polarisTagCB.getBbox().setSw(new LatLong());
		tagListCB.add(polarisTagCB);
		polarisDataCB.setTags(tagListCB);
		com.mmt.hotels.clientgateway.response.moblanding.PolarisData polarisDataCG = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "buildPolarisData", polarisDataCB);
		Assert.assertNotNull(polarisDataCG);
		Assert.assertEquals(polarisDataCG.getTags().size(), 1);
	}

	public void testGetPois(){
		POIInfo poiInfo = new POIInfo();
		poiInfo.setId("1234");
		poiInfo.setCentre(new Centre());
		poiInfo.setMeta(new Meta());
		poiInfo.getMeta().setCategory(new Category());
		poiInfo.getMeta().getCategory().setName("Airport");

		List<Poi> pois = commonResponseTransformer.getPois(Arrays.asList(poiInfo));

		Assert.assertEquals(1, pois.size());
	}

	@Test
	@Ignore
	public void testBuildMedia(){

		List<String> mainImages = Arrays.asList("img1","img2","img3","img4","img5","img6","img7");
		VideoInfo v1 = new VideoInfo();
		v1.setUrl("videourl1");
		VideoInfo v2 = new VideoInfo();
		v1.setUrl("videourl2");
		List<VideoInfo> videoInfoList = Arrays.asList(v1,v2);

		List<MediaInfo> mediaInfoList = commonResponseTransformer.buildMedia(Arrays.asList("imgurl1", "imgurl2"), Arrays.asList(new VideoInfo()),null, false);
		Assert.assertEquals(3, mediaInfoList.size());

		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,utility.getExpDataMap("{VIDEO:0}"), false);
		Assert.assertEquals(5,mediaInfoList.size());
		Assert.assertNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("VIDEO")).findAny().orElse(null));

		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,utility.getExpDataMap("{VIDEO:1}"),false);
		Assert.assertEquals(5,mediaInfoList.size());
		Assert.assertNotNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("VIDEO")).findAny());
		Assert.assertNotNull(mediaInfoList.get(0));
		Assert.assertTrue(mediaInfoList.get(0).getMediaType().equalsIgnoreCase("VIDEO"));

		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,utility.getExpDataMap("{VIDEO:2}"),false);
		Assert.assertEquals(5,mediaInfoList.size());
		Assert.assertNotNull(mediaInfoList.stream().filter(m->m.getMediaType().equalsIgnoreCase("IMAGE")).findAny());
		Assert.assertNotNull(mediaInfoList.get(1));
		Assert.assertTrue(mediaInfoList.get(1).getMediaType().equalsIgnoreCase("VIDEO"));

		mediaInfoList = commonResponseTransformer.buildMedia(mainImages,videoInfoList,utility.getExpDataMap("{VIDEO:3}"),false);
		Assert.assertEquals(5,mediaInfoList.size());
	}

	@Test
	public void test_buildGeoLocation() {
		com.mmt.hotels.model.response.searchwrapper.GeoLocation webApiGeoLocation = new GeoLocation();
		com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation response;

		GeoLocation geoLocation = null;
		response = commonResponseTransformer.buildGeoLocation(geoLocation);
		Assert.assertNull(response);

		webApiGeoLocation.setLatitude("12");
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertNull(response.getLongitude());
		Assert.assertNull(response.getDistance());

		webApiGeoLocation.setLongitude("34");
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertEquals(new Double(34.0d),response.getLongitude());
		Assert.assertNull(response.getDistance());

		webApiGeoLocation.setDistanceMeter(56.0d);
		response = commonResponseTransformer.buildGeoLocation(webApiGeoLocation);
		Assert.assertEquals(new Double(12.0d),response.getLatitude());
		Assert.assertEquals(new Double(34.0d),response.getLongitude());
		Assert.assertEquals(new Double(56.0d),response.getDistance());
	}

	@Test
	public void test_buildReviewSummary() throws IOException {
		Assert.assertNull(commonResponseTransformer.buildReviewSummary(null,null, true));

		HashMap<OTA, JsonNode> map = new Gson().fromJson(FileUtils.readFileToString(ResourceUtils.getFile("classpath:flyfishSummary.json")), HashMap.class);

		HashMap<OTA,JsonNode> webApiMap = new HashMap<>();
		ObjectMapper objectMapper = new ObjectMapper();
		webApiMap.put(OTA.MMT,objectMapper.valueToTree(map.get("MMT")));
		webApiMap.put(OTA.TA,objectMapper.valueToTree(map.get("TA")));

		ReviewSummary response = commonResponseTransformer.buildReviewSummary(null,webApiMap, true);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.TA.name(),response.getSource());

		response = commonResponseTransformer.buildReviewSummary("IN",webApiMap, true);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.MMT.name(),response.getSource());

		webApiMap.put(OTA.EXT,objectMapper.valueToTree(map.get("MMT")));
		webApiMap.remove(OTA.MMT);
		webApiMap.remove(OTA.TA);
		response = commonResponseTransformer.buildReviewSummary("IN",webApiMap, true);
		Assert.assertNotNull(response);
		Assert.assertEquals(OTA.EXT.name(),response.getSource());
	}

	@Test
	public void buildReviewSummaryTest() {
		ReviewSummary response = commonResponseTransformer.buildReviewSummary(null, null, true);
		Assert.assertNull(response);

		com.mmt.hotels.model.response.flyfish.ReviewSummary reviewSummary = new com.mmt.hotels.model.response.flyfish.ReviewSummary();
		reviewSummary.setReviewCount(10);
		reviewSummary.setHotelRating(4.0F);
		reviewSummary.setRatingCount(10);
		reviewSummary.setOta("mmt");

		List<TopicRatings> topicRatings = new ArrayList<>();
		TopicRatings topicRating = new TopicRatings();
		topicRating.setRating(4.0F);
		topicRating.setReviewCount(10);
		topicRating.setTitle("cleanliness");
		topicRatings.add(topicRating);
		reviewSummary.setTopicRatings(topicRatings);

		response = commonResponseTransformer.buildReviewSummary(reviewSummary, true);
		Assert.assertNotNull(response);
		Assert.assertEquals(10, (int) response.getTotalRatingCount());
	}

	@Test
	public void changeReviewSummaryCombinedOTAWhenNotSupported() {
		ReviewSummary response = commonResponseTransformer.buildReviewSummary(null, null, true);
		Assert.assertNull(response);

		com.mmt.hotels.model.response.flyfish.ReviewSummary reviewSummary = new com.mmt.hotels.model.response.flyfish.ReviewSummary();
		reviewSummary.setReviewCount(10);
		reviewSummary.setHotelRating(4.0F);
		reviewSummary.setRatingCount(10);
		reviewSummary.setOta("MMT_BKG");

		List<TopicRatings> topicRatings = new ArrayList<>();
		TopicRatings topicRating = new TopicRatings();
		topicRating.setRating(4.0F);
		topicRating.setReviewCount(10);
		topicRating.setTitle("cleanliness");
		topicRatings.add(topicRating);
		reviewSummary.setTopicRatings(topicRatings);

		response = commonResponseTransformer.buildReviewSummary(reviewSummary, false);
		Assert.assertNotNull(response);
		Assert.assertEquals("MMT", response.getSource());
		Assert.assertEquals(10, (int) response.getTotalRatingCount());
	}

	@Test
	public void buildValueStayTagTest(){
		Map<String, HotelTag> map = commonResponseTransformer.buildValueStaysHotelTag("Sample Title", new HashMap<>());
		Assert.assertTrue(MapUtils.isNotEmpty(map));
	}

	@Test
	public void buildFreeCancellationBenefitsTest() {
		List<com.mmt.hotels.model.response.pricing.FCBenefit> benefitsHes = new ArrayList<>();
		List<FCBenefit> benefitsCG = ReflectionTestUtils
				.invokeMethod(commonResponseTransformer, "buildFreeCancellationBenefits",
						benefitsHes, BNPLVariant.BNPL_AT_0);
		Assert.assertNull(benefitsCG);

		com.mmt.hotels.model.response.pricing.FCBenefit fcBenefitHes = new com.mmt.hotels.model.response.pricing.FCBenefit();
		fcBenefitHes.setType("FC");
		fcBenefitHes.setText("FC till check in");
		benefitsHes.add(fcBenefitHes);

		fcBenefitHes = new com.mmt.hotels.model.response.pricing.FCBenefit();
		fcBenefitHes.setType("FCZPN");
		fcBenefitHes.setText("Zero Payment Now");
		benefitsHes.add(fcBenefitHes);
		benefitsCG = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFreeCancellationBenefits", benefitsHes, BNPLVariant.BNPL_AT_0);
		Assert.assertEquals(benefitsCG.size(), 3);
		Assert.assertEquals(benefitsCG.get(0).getIconType(), IconType.SINGLETICK);
		Assert.assertEquals(benefitsCG.get(1).getIconType(), IconType.DOUBLETICK);
		Assert.assertEquals(benefitsCG.get(2).getIconType(), IconType.DOUBLETICK);
	}

	@Test
	public void testEnableAmenitiesPersuasion() {
		Assert.assertFalse(commonResponseTransformer.enableAmenitiesPersuasion(utility.getExpDataMap("{disableAmenities:1}"), "HOTELS",false));
		Assert.assertTrue(commonResponseTransformer.enableAmenitiesPersuasion(utility.getExpDataMap("{disableAmenities:0}"), "HOTELS",true));
		Assert.assertFalse(commonResponseTransformer.enableAmenitiesPersuasion(null, "HOTELS",false));
	}

	@Test
	public void testEnableSaveValuePersuasion() {
		Assert.assertFalse(commonResponseTransformer.enableSaveValue(utility.getExpDataMap("{disableSaveValue:1}")));
		Assert.assertTrue(commonResponseTransformer.enableSaveValue(utility.getExpDataMap("{disableSaveValue:0}")));
		Assert.assertFalse(commonResponseTransformer.enableSaveValue(null));
	}

	@Test
	public void testBuildFilterCG() {
		Filter filterHes = new Filter();
		filterHes.setFilterGroup(STAR_RATING);
		filterHes.setFilterRange(new FilterRange());
		filterHes.setSequence(0);
		filterHes.setSuggestedFilters(new ArrayList<>());
		Filter suggestedFilter = new Filter();
		suggestedFilter.setFilterGroup(LOCATION);
		filterHes.getSuggestedFilters().add(suggestedFilter);
		Assert.assertNotNull(commonResponseTransformer.buildFilterCG(filterHes));
	}

	@Test
	public void testBuildSearchContext() {
		String lob = "HOTELS";
		String cityCode = "CTDEL";
		String countryCode = "IN";
		String tripType = "ONEWAY";
		CardPayloadResponse cardPayload = new CardPayloadResponse();
		AdTechSearchContext searchContext = new AdTechSearchContext();
		ArrayList<AdTechSearchContextDetails> currSearchContext = new ArrayList<>();
		AdTechSearchContextDetails searchContextDetails = new AdTechSearchContextDetails();
		searchContextDetails.setLob(lob);
		AdTechDestination destination = new AdTechDestination();
		destination.setCityCode(cityCode);
		destination.setCountryCode(countryCode);
		searchContextDetails.setDestination(destination);
		Date startDate = Date.from(Instant.now());
		searchContextDetails.setStartDate(startDate.getTime());
		Date endDate = Date.from(Instant.now().plus(1, ChronoUnit.DAYS));
		searchContextDetails.setEndDate(endDate.getTime());
		searchContextDetails.setTripType(tripType);
		AdTechHotel hotels = new AdTechHotel();
		AdTechPaxDetails adTechPaxDetails = new AdTechPaxDetails();
		adTechPaxDetails.setAdult(2);
		adTechPaxDetails.setChildren(3);
		hotels.setRoomCount(2);
		hotels.setPaxDetails(adTechPaxDetails);
		searchContextDetails.setHotels(hotels);
		currSearchContext.add(searchContextDetails);
		searchContext.setCurrSearchContext(currSearchContext);
		cardPayload.setSearchContext(searchContext);
		com.mmt.hotels.clientgateway.response.moblanding.AdTechSearchContext searchContextCG = commonResponseTransformer.buildSearchContext(cardPayload);
		Assert.assertNotNull(searchContextCG);
		Assert.assertNotNull(searchContextCG.getCurrSearchContext());
		Assert.assertEquals(lob, searchContextCG.getCurrSearchContext().get(0).getLob());
		Assert.assertNotNull(searchContextCG.getCurrSearchContext().get(0).getDestination());
		Assert.assertNotNull(searchContextCG.getCurrSearchContext().get(0).getHotels());
		Assert.assertEquals(tripType, searchContextCG.getCurrSearchContext().get(0).getTripType());

	}

	@Test
	public void testBuildSkipApprovalReasons() {
		List<com.mmt.hotels.model.response.corporate.ReasonForSkipApproval> skipApprovalReasonsHES = new ArrayList<>();
		com.mmt.hotels.model.response.corporate.ReasonForSkipApproval reasonForSkipApproval= new ReasonForSkipApproval();
		reasonForSkipApproval.setEnablePersonalCorpBooking(true);
		reasonForSkipApproval.setText("abc");
		reasonForSkipApproval.setInputType("inputtype");
		skipApprovalReasonsHES.add(reasonForSkipApproval);
		List<com.mmt.hotels.clientgateway.response.corporate.ReasonForSkipApproval> response = commonResponseTransformer.buildSkipApprovalReasons(skipApprovalReasonsHES);
		Assert.assertNotNull(response);
	}

	@Test
	public void testBuildGeoLocation() {
		double latitude = 28.64163;
		double longitude = 77.21613;
		HotelResult hotelResult = new HotelResult();
		hotelResult.setLatitude(latitude);
		hotelResult.setLongitude(longitude);
		com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation geoLocation = commonResponseTransformer.buildGeoLocation(hotelResult);
		assertNotNull(geoLocation);
		assertEquals(latitude, geoLocation.getLatitude(), 0.0);
		assertEquals(longitude, geoLocation.getLongitude(), 0.0);
	}

	@Test
	public void buildCorpAutobookRequestorConfigTest() {
		AutobookRequestorConfigBO autobookRequestorConfigHES = new AutobookRequestorConfigBO();
		autobookRequestorConfigHES.setTitle("title");
		autobookRequestorConfigHES.setSubTitle("subtitle");
		CorpAutobookRequestorConfigBO corpAutobookRequestorConfigCG = commonResponseTransformer.buildCorpAutobookRequestorConfig(autobookRequestorConfigHES);
		Assert.assertNotNull(corpAutobookRequestorConfigCG);
		Assert.assertEquals("title", corpAutobookRequestorConfigCG.getTitle());
		Assert.assertEquals("subtitle", corpAutobookRequestorConfigCG.getSubTitle());
	}

	@Test
	public void testGetPriceDetailForHeaderDetails(){
		TotalPricing	 totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, null, true);
	}

	@Test
	public void testGetPriceDetailForSlotDetails(){
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);
		DayUseRoomsResponse dayUseRoomsResponse = new DayUseRoomsResponse();
		DayUseSlotPlan slotPlan = new DayUseSlotPlan();
		commonResponseTransformer.getPriceDetail(totalPricing, dayUseRoomsResponse, slotPlan, false);
	}

	@Test
	public void testBuildPaymentPlan(){
		com.mmt.hotels.model.response.pricing.PaymentPlan paymentPlan = new PaymentPlan();
		paymentPlan.setAmount(100.0);
		List<PaymentPlan> paymentPlans = new ArrayList<>();
		paymentPlans.add(new PaymentPlan());
		paymentPlan.setPaymentPolicy(paymentPlans);

		when(polyglotService.getTranslatedData(PAYMENT_PLAN_PENALTY_TEXT)).thenReturn("Payment Panelty");
		com.mmt.hotels.clientgateway.response.rooms.PaymentPlan paymentPlanCG = commonResponseTransformer.buildPaymentPlan(paymentPlan);
		Assert.assertEquals("Payment Panelty", paymentPlanCG.getPenaltyText());
	}

	@Test
	public void testBuildMyBizQuickPayConfig() {
		when(polyglotService.getTranslatedData(anyString())).thenReturn("abc");
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		MyBizQuickPayConfigBO myBizQuickPayConfigBO = commonResponseTransformer.buildMyBizQuickPayConfig(displayPriceBreakDown, "USD");
		Assert.assertNotNull(myBizQuickPayConfigBO);

	}

	@Test
	public void testBuildMyBizQuickPayConfigWithCurrencySymbol() {
		when(polyglotService.getTranslatedData(anyString())).thenAnswer(invocation -> {
			if (ConstantsTranslation.MYBIZ_QUICKPAY_TEXT_CURRENCY.equals(invocation.getArgument(0))) {
				return "<b>{CURRENCY_SYMBOL}{TOTAL_AMOUNT}</b>({CURRENCY_SYMBOL}{PRICE_AFTER_DISCOUNT} + {CURRENCY_SYMBOL}{HOTEL_TAXES} convenience fee) will be debited from your organisation myBiz wallet on clicking <b>PAY NOW</b>";
			}
			return "abc";
		});
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(100.0);
		displayPriceBreakDown.setDisplayPrice(200.0);
		MyBizQuickPayConfigBO myBizQuickPayConfigBO = commonResponseTransformer.buildMyBizQuickPayConfig(displayPriceBreakDown, "USD");
		Assert.assertNotNull(myBizQuickPayConfigBO);
		Assert.assertEquals("<b>$200.0</b>($100.0 + $100.0 convenience fee) will be debited from your organisation myBiz wallet on clicking <b>PAY NOW</b>", myBizQuickPayConfigBO.getText());
	}

	@Test
	public void testBuildCardInfo() {
		com.mmt.hotels.pojo.listing.personalization.CardData cardData = new CardData();
		List<com.mmt.hotels.pojo.listing.personalization.CardData> cardDataList = new ArrayList<>();
		cardDataList.add(cardData);
		com.mmt.hotels.pojo.listing.personalization.CardAction cardAction = new CardAction();
		List<com.mmt.hotels.pojo.listing.personalization.CardAction> cardActionList = new ArrayList<>();
		cardActionList.add(cardAction);
		cardData.setCardAction(cardActionList);
		cardData.setHotelList(new ArrayList<>());
		cardData.getHotelList().add(new SearchWrapperHotelEntity());
		cardData.getHotelList().get(0).setDisplayFare(new DisplayFare());
		cardData.getHotelList().get(0).getDisplayFare().setDisplayPriceBreakDown(new DisplayPriceBreakDown());
		cardData.setCardSubType("HIDDEN_GEM_CARD");
		cardData.setHotelList(new ArrayList<>());
		cardData.getHotelList().add(new SearchWrapperHotelEntity());
		cardData.getHotelList().get(0).setDisplayFare(new DisplayFare());
		cardData.getHotelList().get(0).getDisplayFare().setDisplayPriceBreakDown(new DisplayPriceBreakDown());
		cardData.getHotelList().get(0).setFlyfishReviewSummary(new HashMap<>());
		cardData.getHotelList().get(0).setCountryCode("BD");
		cardData.setImageList(Arrays.asList("img1", "img2"));
		CardInfo cardInfo = commonResponseTransformer.buildCardInfo(cardDataList, "DESKTOP", new LinkedHashMap<>());
		Assert.assertNotNull(cardInfo);
		cardData.getHotelList().get(0).getFlyfishReviewSummary().put(OTA.MMT, null);
		cardInfo = commonResponseTransformer.buildCardInfo(cardDataList, "DESKTOP", new LinkedHashMap<>());
		Assert.assertNotNull(cardInfo);
		cardData.getHotelList().get(0).setCountryCode("IN");
		com.mmt.hotels.pojo.listing.personalization.RushDealTimerInfo rushDealTimerInfo = new com.mmt.hotels.pojo.listing.personalization.RushDealTimerInfo();
		rushDealTimerInfo.setDesc("Rush Deal");
		rushDealTimerInfo.setValidityTimestamp("15267378182");
		BgGradient bgGradient = new BgGradient();
		bgGradient.setStart("#FFFFFF");
		bgGradient.setEnd("#000000");
		bgGradient.setCenter("#FFFFFF");
		bgGradient.setDirection("horizontal");
		rushDealTimerInfo.setBgGradient(bgGradient);
		cardData.setRushDealTimerInfo(rushDealTimerInfo);
		cardInfo = commonResponseTransformer.buildCardInfo(cardDataList, "DESKTOP", new LinkedHashMap<>());
		Assert.assertEquals(2,cardInfo.getImageList().size());
		Assert.assertNotNull(cardInfo);
		Assert.assertNotNull(cardInfo.getRushDealTimerInfo());
		Assert.assertEquals("Rush Deal", cardInfo.getRushDealTimerInfo().getDesc());
		Assert.assertEquals("15267378182", cardInfo.getRushDealTimerInfo().getValidityTimestamp());
		Assert.assertNotNull(cardInfo.getRushDealTimerInfo().getBgGradient());
		Assert.assertEquals(cardInfo.getRushDealTimerInfo().getBgGradient().getStart(),"#FFFFFF");
		Assert.assertEquals(cardInfo.getRushDealTimerInfo().getBgGradient().getEnd(),"#000000");
		Assert.assertEquals(cardInfo.getRushDealTimerInfo().getBgGradient().getCenter(),"#FFFFFF");
		Assert.assertEquals(cardInfo.getRushDealTimerInfo().getBgGradient().getDirection(),"horizontal");
	}

	@Test
	public void testGetHotelRatingSummary() {
		TravellerRatingSummaryDTO travellerRatingSummaryDTO = new TravellerRatingSummaryDTO();
		List<ConceptSummaryDTO> hotelSummaryList = new ArrayList<>();
		ConceptSummaryDTO conceptSummaryDTO = new ConceptSummaryDTO();
		SubConceptDTO subConcept = new SubConceptDTO();
		List<SubConceptDTO> subConcepts = new ArrayList<>();
		subConcepts.add(subConcept);
		conceptSummaryDTO.setSubConcepts(subConcepts);
		hotelSummaryList.add(conceptSummaryDTO);
		travellerRatingSummaryDTO.setHotelSummary(hotelSummaryList);
		List<ConceptSummary> resp = commonResponseTransformer.getHotelRatingSummary(travellerRatingSummaryDTO);
		Assert.assertNotNull(resp);
	}

	@Test
	public void testBuildResponseForBooking() {
		com.mmt.hotels.model.response.corporate.ReasonForBooking reasonForBooking = new ReasonForBooking();
		List<com.mmt.hotels.model.response.corporate.ReasonForBooking> reasons = new ArrayList<>();
		reasons.add(reasonForBooking);
		List<com.mmt.hotels.clientgateway.response.corporate.ReasonForBooking> response = commonResponseTransformer.buildReasonForBooking(reasons);
		Assert.assertNotNull(response);

	}

	@Test
	public void testSetBnplNewVariantDetails() {
		String bnplNewVariantText = "Book now @ just Rs. 1";
		String bnplNewVariantSubText = "Pay the remaining amount using any paymode any time before 19 Jun";
		BNPLDetails bnplDetails = new BNPLDetails();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "setBnplNewVariantDetails", bnplNewVariantText, bnplNewVariantSubText, bnplDetails);
		Assert.assertNotNull(bnplDetails.getBnplNewVariantText());
		Assert.assertEquals(bnplNewVariantText, bnplDetails.getBnplNewVariantText());
		Assert.assertNotNull(bnplDetails.getBnplNewVariantSubText());
		Assert.assertEquals(bnplNewVariantSubText, bnplDetails.getBnplNewVariantSubText());
	}

	@Test
	public void testBuildValueStaysData() {
		ValueStaysData valueStaysData = new ValueStaysData();
		valueStaysData.setStarText("star text");
		ValueStaysPersuasion valueStaysPersuasion = new ValueStaysPersuasion();
		valueStaysPersuasion.setText("text");
		valueStaysPersuasion.setIconUrl("iconUrl");
		valueStaysPersuasion.setIconType("iconType");
		valueStaysData.setValueStaysPersuasion(valueStaysPersuasion);
		ValueStaysDataCG valueStaysDataCG = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildValueStaysData", valueStaysData);
		Assert.assertNotNull(valueStaysDataCG);
	}

	@Test
	public void buildChildAttributesCgFromHesTest(){
		List<com.mmt.model.AttributesFacility> attributesFacilityList = new ArrayList<>();
		AttributesFacility attributesFacility = new AttributesFacility();
		attributesFacility.setName("test");
		attributesFacilityList.add(attributesFacility);
		commonResponseTransformer.buildChildAttributesCgFromHes(attributesFacilityList);
	}

	@Test
	public void enableDiscountTest(){
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setTotalSaving(201);
		displayPriceBreakDown.setDisplayPrice(200);
		commonResponseTransformer.enableDiscount(displayPriceBreakDown);
	}

	@Test
	public void buildHighlightedAmenitiesTest(){
		Set<String> highlights = new HashSet<>();
		highlights.add("Hello");
		highlights.add("hi");
		commonResponseTransformer.buildHighlightedAmenities(highlights);
	}

	@Test
	public void buildAmenitiesTest(){
		List<com.mmt.model.FacilityGroup> facilityWithGrp = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		facilityGroup.setName("Hello");
		List<Facility> facilities = new ArrayList<>();
		Facility facility = new Facility();
		facility.setName("Helloq");
		facilities.add(facility);
		facilityGroup.setFacilities(facilities);
		facilityWithGrp.add(facilityGroup);
		RoomInfo roomInfo = new RoomInfo();
		roomInfo.setHighlightedFacilities(facilityWithGrp);
		commonResponseTransformer.buildAmenities(facilityWithGrp,facilityWithGrp, roomInfo.getHighlightedFacilities(),  true);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHesNull() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, (Object) null);
		Assert.assertNull(paymentDate);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHes_splitLengthZero() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, "");
		Assert.assertNull(paymentDate);
	}

	@Test
	public void buildPaymentDateTest_paymentDateHes_splitLengthNonZero() throws Exception {
		java.lang.reflect.Method method = commonResponseTransformer.getClass().getDeclaredMethod("buildPaymentDate", String.class);
		method.setAccessible(true);
		String paymentDate = (String) method.invoke(commonResponseTransformer, "26 June, 8:30 AM");
		Assert.assertEquals(paymentDate, "26 June");
	}

	@Test
	public void testBuildInclusionListOld_withInclusions() throws Exception {
		List<com.mmt.hotels.model.response.pricing.Inclusion> inclusions = new ArrayList<>();
		com.mmt.hotels.model.response.pricing.Inclusion inclusion1 = new com.mmt.hotels.model.response.pricing.Inclusion();
		inclusion1.setCode("Free Breakfast");
		com.mmt.hotels.model.response.pricing.Inclusion inclusion2 = new com.mmt.hotels.model.response.pricing.Inclusion();
		inclusion2.setCode("Free WiFi");
		inclusions.add(inclusion1);
		inclusions.add(inclusion2);

		Method method = CommonResponseTransformer.class.getDeclaredMethod("buildInclusionListOld", List.class);
		method.setAccessible(true);

		List<String> result = (List<String>) method.invoke(commonResponseTransformer, inclusions);

		Assert.assertNotNull(result);
		Assert.assertEquals(2, result.size());
		Assert.assertTrue(result.contains("Free Breakfast"));
		Assert.assertTrue(result.contains("Free WiFi"));
	}

	@Test
	public void buildPaymentInfoTextForAlternateCurrencyTest() {
		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(null, "IN", "PAS", "INR");

		TotalPricing totalPricing = new TotalPricing();

		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");
		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, "US", "PAS", "USD");
		Assert.assertTrue(totalPricing.getPaymentInfoText()==null);

		MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, "IN", "PAS", "USD");
		Assert.assertTrue(totalPricing.getPaymentInfoText()==null);

		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, "US", "PAH", "USD");
		Assert.assertTrue(totalPricing.getPaymentInfoText()==null);

		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, "US", "PAS", "INR");
		Assert.assertTrue(totalPricing.getPaymentInfoText()==null);

		commonResponseTransformer.buildPaymentInfoTextForAlternateCurrency(totalPricing, "US", "PAS", "USD");
		Assert.assertTrue(totalPricing.getPaymentInfoText().equals("PAYMENT_INR_MSG"));

	}

	@Test
	public void getRoomsSubTextTest() {
		Assert.assertEquals("Entire Apartments", commonResponseTransformer.getRoomsSubText(2, "entire", "Apartment"));
		Assert.assertEquals("Entire Apartment", commonResponseTransformer.getRoomsSubText(1, "entire", "Apartment"));
		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOMS)).thenReturn("Rooms");
		Mockito.when(polyglotService.getTranslatedData(ConstantsTranslation.ADDITIONAL_FEE_SUBTEXT_ROOM)).thenReturn("Room");
		Assert.assertEquals("Rooms", commonResponseTransformer.getRoomsSubText(2, "room", "Apartment"));
		Assert.assertEquals("Room", commonResponseTransformer.getRoomsSubText(1, "room", "Apartment"));
	}

	@Test
	public void buildNearbyDataTest() {

		List<com.mmt.hotels.model.response.nearby.NearByLocation> nearByData = new ArrayList<>();
		com.mmt.hotels.model.response.nearby.NearByLocation nearByLocation = new NearByLocation();
		nearByData.add(nearByLocation);
		List<com.mmt.hotels.clientgateway.response.moblanding.NearByLocation> nearbyList = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildNearByData", nearByData);
		Assert.assertEquals(1, nearbyList.size());

	}

	@Test
	public void buildCardReviewSummaryTest() {
		Map<OTA, JsonNode> reviewSummaryMap = new HashMap<>();
		JsonNode jsonMMT = new IntNode(1);
		JsonNode taMMT = new IntNode(2);

		reviewSummaryMap.put(OTA.MMT, jsonMMT);
		reviewSummaryMap.put(OTA.TA, taMMT);

		ReviewSummary reviewSummary = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardReviewSummary", "IN", reviewSummaryMap);
		Assert.assertNotNull(reviewSummary);
	}

	@Test
	public void buildSelectiveHotelPersuasionsTest() {

		SearchWrapperHotelEntity sw = new SearchWrapperHotelEntity();
		Hotel hotel = new Hotel();
		sw.setSelectiveHotelPersuasions(new SelectiveHotelPersuasions());
		commonResponseTransformer.buildSelectiveHotelPersuasions(hotel, sw);
		Assert.assertNotNull(hotel.getSelectiveHotelPersuasions());

	}

	@Test
	public void buildCardErrorTest() {
		CardError cardError = new CardError();
		com.mmt.hotels.clientgateway.response.moblanding.CardError cardErr = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardError", cardError);
		Assert.assertNotNull(cardErr);
	}

	@Test
	public void getBhfPersuasionsTest() {
		List<com.mmt.hotels.pojo.response.detail.BhfPersuasion> bhfPersuasions = new ArrayList<>();
		com.mmt.hotels.pojo.response.detail.BhfPersuasion bhfPersuasion = new BhfPersuasion();
		bhfPersuasions.add(bhfPersuasion);
		List<com.mmt.hotels.clientgateway.response.BhfPersuasion> persuasionList = commonResponseTransformer.getBhfPersuasions(bhfPersuasions);
		Assert.assertEquals(1, persuasionList.size());
		persuasionList = commonResponseTransformer.getBhfPersuasions(new ArrayList<>());
		Assert.assertNull(persuasionList);
	}

	@Test
	public void testGetPricingDetails() {
		DisplayPriceBreakDown displayPriceBreakDown = buildDisplayPriceBreakdown();
		String expData = "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(displayPriceBreakDown, "IN", "PAS", false, "1136", expData, false, false, "insurance", false,false, "FIRST_FIVE_BOOKING",false, false);
		assertTrue(CollectionUtils.isNotEmpty(pricingDetails));

		DisplayPriceBreakDown displayPriceBreakDown1 = buildDisplayPricerBreakdownForSupplier();
		List<PricingDetails> pricingDetails1 = commonResponseTransformer.getPricingDetails(displayPriceBreakDown1, "IN", "PAS", false, "1136", expData, false, false, "insurance",false,false, "FIRST_FIVE_BOOKING",false, false);
		assertTrue(CollectionUtils.isNotEmpty(pricingDetails1));
		Optional<PricingDetails> optionalPricingDetails = pricingDetails1.stream().filter(f->f.getLabel().equalsIgnoreCase(TOTAL_DISCOUNT_LABEL)).findAny();
		assertTrue(optionalPricingDetails.isPresent());
		PricingDetails disount = optionalPricingDetails.get();
		boolean promotionalDiscount = disount.getBreakup().stream().anyMatch(e -> e.getLabel().equalsIgnoreCase(PromotionalOfferType.EARLY_BIRD.name() + "_DISC_LABEL"));
		assertTrue(promotionalDiscount);
	}

	@Test
	public void testGetPricingDetailsWithMyPartnerTdsTaxStructureForDetailsPage() {
		DisplayPriceBreakDown displayPriceBreakDown = buildDisplayPriceBreakdown();
		String expData = "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.DETAIL_SEARCH_ROOMS);
		List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(displayPriceBreakDown, "IN", "PAS", false, "1136", expData, false, true, "insurance", false,false,"FIRST_FIVE_BOOKING",false, false);
		assertTrue(CollectionUtils.isNotEmpty(pricingDetails));
		assertEquals(7, pricingDetails.size());
		assertThat(pricingDetails.get(0).getAmount(), Matchers.is(10.0)); // Base Fare
		assertThat(pricingDetails.get(1).getAmount(), Matchers.is(2.0)); // Wallet discount
		assertThat(pricingDetails.get(2).getAmount(), Matchers.is(4.0)); // Taxes
		assertThat(pricingDetails.get(3).getAmount(), Matchers.is(9.0)); // Total Amount to be paid
	}

	@Test
	public void testGetPricingDetailsWithMyPartnerTdsTaxStructureForReviewPage() {
		DisplayPriceBreakDown displayPriceBreakDown = buildDisplayPriceBreakdown();
		String expData = "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);
		List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(displayPriceBreakDown, "IN", "PAS", false, "1136", expData, false, true, "insurance", false,false,"FIRST_FIVE_BOOKING",false, false);
		assertTrue(CollectionUtils.isNotEmpty(pricingDetails));
		assertEquals(7, pricingDetails.size());
		assertThat(pricingDetails.get(0).getAmount(), Matchers.is(10.0)); // Base Fare
		assertThat(pricingDetails.get(1).getAmount(), Matchers.is(3.0)); // Partner Commission
		assertThat(pricingDetails.get(2).getAmount(), Matchers.is(2.0)); // Hotel discount
		assertThat(pricingDetails.get(3).getAmount(), Matchers.is(2.0)); // Wallet discount
		assertThat(pricingDetails.get(4).getAmount(), Matchers.is(4.0)); // Taxes
		assertThat(pricingDetails.get(5).getAmount(), Matchers.is(11d)); // Total Amount to be paid
	}

	@Test
	public void testGetPricingDetailsIsServiceFeeGstIncludedTaxStructureForReviewPage() {
		DisplayPriceBreakDown displayPriceBreakDown = buildDisplayPriceBreakdown();
		displayPriceBreakDown.setServiceFeeGstIncluded(true);
		displayPriceBreakDown.setMmtServiceCharge(25.0d);
		when(commonConfigConsul.getServiceFeeGstPercentage()).thenReturn(18);
		String expData = "{APE:10,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T,ADDON:T,CHPC:T,AARI:T,NLP:Y,RCPN:T,PLRS:T,MMRVER:V3,BLACK:T,IAO:T,EMIDT:1,NEWTY:T,AIP:T,TFT:T,GEC:F}";
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);
		List<PricingDetails> pricingDetails = commonResponseTransformer.getPricingDetails(displayPriceBreakDown, "IN", "PAS", false, "1136", expData, false, true, "insurance", false,false,"FIRST_FIVE_BOOKING",false, false);
		assertTrue(CollectionUtils.isNotEmpty(pricingDetails));
		assertEquals(7, pricingDetails.size());
		assertThat(pricingDetails.get(4).getBreakup().get(2).getAmount(), Matchers.is(5.0)); // Service Fee Gst Amount
	}

	private DisplayPriceBreakDown buildDisplayPriceBreakdown() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(10.0d);
		displayPriceBreakDown.setHotelTax(2.0d);
		displayPriceBreakDown.setCdfDiscount(1.0d);
		displayPriceBreakDown.setMmtDiscount(2.0d);
		displayPriceBreakDown.setWallet(2.0d);
		displayPriceBreakDown.setMmtServiceCharge(2.0d);
		displayPriceBreakDown.setDisplayPrice(9.0d);
		displayPriceBreakDown.setFlexiCancellationCharges(2d);
		return displayPriceBreakDown;
	}

	private DisplayPriceBreakDown buildDisplayPricerBreakdownForSupplier() {
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setBasePrice(3960.0d);
		displayPriceBreakDown.setHotelTax(277.0d);
		displayPriceBreakDown.setTotalTax(439.0d);
		displayPriceBreakDown.setCdfDiscount(231.0d);
		displayPriceBreakDown.setMmtDiscount(1644.0d);
		displayPriceBreakDown.setWallet(0.0d);
		displayPriceBreakDown.setEffectivePrice(2085.0d);
		displayPriceBreakDown.setTotalAmount(2085.0d);
		displayPriceBreakDown.setSavingPerc(47.0d);
		displayPriceBreakDown.setNonDiscountedPrice(3960.0d);
		displayPriceBreakDown.setMmtServiceCharge(162.0d);
		displayPriceBreakDown.setDisplayPrice(2085.0d);
		displayPriceBreakDown.setOfferDiscountBreakup(new HashMap<>());
		displayPriceBreakDown.getOfferDiscountBreakup().put(PromotionalOfferType.MIXED,1644.d);
		displayPriceBreakDown.setSupplierDealsDetailMap(new HashMap<>());
		displayPriceBreakDown.getSupplierDealsDetailMap().put("deal", PromotionalOfferType.EARLY_BIRD.getName());
		displayPriceBreakDown.getSupplierDealsDetailMap().put("discount", "1644");
		return displayPriceBreakDown;
	}

	@Test
	public void updateCancellationPolicyFCSubtext() {
		MDC.clear();
		BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
		bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
		bookedCancellationPolicy.setSubText("test");

		when(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_PEAK_SUBTEXT)).thenReturn("test1 <DateTime> <zone>");
		when(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_NONPEAK_SUBTEXT)).thenReturn("test2 <DateTime> <zone>");

		commonResponseTransformer.updateCancellationPolicyFCSubtext(bookedCancellationPolicy, true, "1", false,null);
		assertEquals(bookedCancellationPolicy.getSubText(), "test. test1 1 IST");

		bookedCancellationPolicy.setSubText("test");
		commonResponseTransformer.updateCancellationPolicyFCSubtext(bookedCancellationPolicy, false, "2", false, null);
		assertEquals(bookedCancellationPolicy.getSubText(), "test. test2 2 IST");
		MDC.clear();
	}

	@Test
	public void buildBNPLDetailsForBNPLDisabledTest() {
		BNPLDetails bnplDetails = null;

		when(polyglotService.getTranslatedData(BNPL_SINGLE_BOOKING_THRESHOLD_SUBTEXT)).thenReturn("Your active booking {active_count}. Cancel {cancel_count} to continue");
		when(polyglotService.getTranslatedData(BNPL_MULTIPLE_BOOKING_THRESHOLD_SUBTEXT)).thenReturn("Your active booking {active_count}. Cancel {cancel_count} to continue");
		when(polyglotService.getTranslatedData(NON_BNPL_COUPON_APPLIED_SUBTEXT)).thenReturn("Please remove {coupon_code} to get bnpl");
		when(polyglotService.getTranslatedData(INSURANCE_BNPL_SUBTEXT)).thenReturn("Please remove assurance to avail bnpl");

		// Single ACTIVE booking cancel  case
		bnplDetails = commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD,"test", BNPLVariant.BNPL_AT_0,3, 3);
		assertEquals(bnplDetails.getBnplNewVariantSubText(),"Your active booking 3. Cancel 1 to continue");

		// Multiple ACTIVE booking cancel case
		bnplDetails = commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD,"test", BNPLVariant.BNPL_AT_0,5, 3);
		assertEquals(bnplDetails.getBnplNewVariantSubText(),"Your active booking 5. Cancel 3 to continue");

		// Non BNPL coupon case
		bnplDetails = commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, "test", BNPLVariant.BNPL_AT_1, null, 3);
		assertEquals(bnplDetails.getBnplNewVariantSubText(),"Please remove test to get bnpl");

		// Assurance case
		bnplDetails = commonResponseTransformer.buildBNPLDetailsForDisabledBnpl(BNPLDisabledReason.INSURANCE_APPLIED, "test", BNPLVariant.BNPL_AT_0, null, 3);
		assertEquals(bnplDetails.getBnplNewVariantSubText(), "Please remove assurance to avail bnpl");

	}

	@Test
	public void getBNPLDisabledReasonTest() {

		// Priority to show bnpl disabled: ACTIVE_BOOKINGS > NON-BNPL COUPON > INSURANCE ADD=ON
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, true));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, false, false));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, true, false));
		assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, commonResponseTransformer.getBNPLDisabledReason(true, false, true));

		assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, true, true));
		assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, true, false));

		assertEquals(BNPLDisabledReason.INSURANCE_APPLIED, commonResponseTransformer.getBNPLDisabledReason(false, false, true));
	}



	@Test
	public void buildBNPLPriceFooterTest() {
		PriceFooter priceFooter = commonResponseTransformer.buildBNPLPriceFooter();
		Assert.assertNotNull(priceFooter);
		Assert.assertNotNull(priceFooter.getCtaText());
		Assert.assertNotNull(priceFooter.getAmountText());
		Assert.assertNotNull(priceFooter.getText());
		Assert.assertNotNull(priceFooter.getSubtext());
	}

	@Test
	public void getAddonInfoTest() {
		Map<String, String> addOnInfoTag = new HashMap<>();
		addOnInfoTag.put("title", "test");
		addOnInfoTag.put("color", "test");
		ReflectionTestUtils.setField(commonResponseTransformer, "addOnInfoTag", addOnInfoTag);
		AddOnInfo addonInfo = commonResponseTransformer.getAddonInfo(true, 120d,"in",new HashMap<>(), new ArrayList<>(), AddOnState.UNSELECTED);
		Assert.assertNotNull(addonInfo);
		Assert.assertNotNull(addonInfo.getTitle());
		Assert.assertNotNull(addonInfo.getSubtitle());
		Assert.assertNotNull(addonInfo.getTag());
		Assert.assertEquals(addonInfo.getTag().getTitle(), "test");
		Assert.assertEquals(addonInfo.getTag().getColor(), "test");

	}

	@Test
	public void buildScarcityHotelTagTest() {
		MDC.clear();
		HotelRates hotelRates = new HotelRates();
		AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
		availRoomsResponse.setHotelInfo(new com.mmt.hotels.clientgateway.response.staticdetail.HotelResult());
		commonResponseTransformer.buildScarcityHotelTag(hotelRates, availRoomsResponse);

		hotelRates.setRoomTypeDetails(new RoomTypeDetails());
		hotelRates.getRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
		hotelRates.getRoomTypeDetails().getTotalDisplayFare().setTotalRoomCount(3);
		hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
		RoomType roomType = new RoomType();
		roomType.setRatePlanList(new HashMap<>());
		com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
		ratePlan.setAvailDetails(new AvailDetails());
		ratePlan.getAvailDetails().setCount(6);
		roomType.getRatePlanList().put("ratePlan", ratePlan);
		hotelRates.getRoomTypeDetails().getRoomType().put("room", roomType);
		commonResponseTransformer.buildScarcityHotelTag(hotelRates, availRoomsResponse);
		Assert.assertNotNull(availRoomsResponse.getAlerts());
		Assert.assertEquals(availRoomsResponse.getAlerts().size(), 1);
		Assert.assertEquals(availRoomsResponse.getAlerts().get(0).getType(), AlertType.PRICE_INCREASE);

		MDC.clear();
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), DEVICE_IOS);
		commonResponseTransformer.buildScarcityHotelTag(hotelRates, availRoomsResponse);
		Assert.assertNotNull(availRoomsResponse.getHotelInfo().getHotelTags());
		Assert.assertNotNull(availRoomsResponse.getHotelInfo().getHotelTags().get("PC_HOTEL_TOP"));
		Assert.assertEquals(availRoomsResponse.getHotelInfo().getHotelTags().get("PC_HOTEL_TOP").getType(), HotelTagType.SCARCITY.getValue());
		MDC.clear();

	}

	/**
	 * test checks if black info persuasion is set correctly for total pricing
	 */

	@Test
	public void setPricePersuasionBlackInfoTest() {
		BlackInfo blackInfo = new BlackInfo();
		TotalPricing totalPricing = new TotalPricing();
		when(persuasionUtil.buildBlackPersuasionForReviewPage(blackInfo, false)).thenReturn(new Persuasion());
		commonResponseTransformer.setPricePersuasionBlackInfo(blackInfo, totalPricing);
		Assert.assertNotNull(totalPricing.getPricePersuasions());
		Assert.assertNotNull(totalPricing.getPricePersuasions().get(PLACEHOLDER_PRICE_BOTTOM));

	}

	@Test
	public void buildTopSectionPersuasionTest(){

		Hotel hotel = new Hotel();
		SearchWrapperHotelEntity hotelEntity = new SearchWrapperHotelEntity();
		hotelEntity.setTag("PREFERRED_BY_COMPANY");
		Map<Object,Object> inputHotelPersuasions = new HashMap<>();
		hotel.setHotelPersuasions(inputHotelPersuasions);
		when(polyglotService.getTranslatedData("PREFERRED_BY_COMPANY")).thenReturn("Preferred By Your Company");
		commonResponseTransformer.buildTopSectionPersuasion(hotel,"PREFERRED_BY_COMPANY",Constants.PERSONALISED_PICKS_HOTELS,Constants.CLIENT_DESKTOP);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		Map<Object,Object> hotelPersuasions = (Map<Object,Object>) hotel.getHotelPersuasions();
		Assert.assertNotNull(hotelPersuasions.get("PC_TOP_SECTION"));

		commonResponseTransformer.buildTopSectionPersuasion(hotel,"PREFERRED_BY_COMPANY",Constants.PERSONALISED_PICKS_HOTELS,Constants.ANDROID);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		hotelPersuasions = (Map<Object,Object>) hotel.getHotelPersuasions();
		Assert.assertNotNull(hotelPersuasions.get("PLACEHOLDER_CARD_M6"));

		commonResponseTransformer.buildTopSectionPersuasion(hotel,"PREFERRED_BY_COMPANY",Constants.LISTING_MAP,Constants.CLIENT_DESKTOP);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		hotelPersuasions = (Map<Object,Object>) hotel.getHotelPersuasions();
		Assert.assertNotNull(hotelPersuasions.get("PC_TOP_SECTION"));

		commonResponseTransformer.buildTopSectionPersuasion(hotel,"PREFERRED_BY_COMPANY",Constants.LISTING_MAP,Constants.ANDROID);
		Assert.assertNotNull(hotel.getHotelPersuasions());
		hotelPersuasions = (Map<Object,Object>) hotel.getHotelPersuasions();
		Assert.assertNotNull(hotelPersuasions.get("PLACEHOLDER_CARD_M6"));
	}

	@Test
	public void buildNoCostEmiDetailAndUpdateFullPaymentTest() {
		FullPayment fullPaymentResponse = commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(null,null, null, null);
		Assert.assertNull(fullPaymentResponse);

		NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
		noCostEmiDetails.setCouponCode("ICICIEMI");
		noCostEmiDetails.setEmiAmount(5000);
		noCostEmiDetails.setBankName("ICICI");
		noCostEmiDetails.setTenure(3);
		List<NoCostEmiDetails> noCostEmiDetailsList = new ArrayList<>();
		noCostEmiDetailsList.add(noCostEmiDetails);

		List<PricingDetails> pricingDetailsList = new ArrayList<>();
		PricingDetails pricingDetails = new PricingDetails();
		pricingDetails.setKey("TOTAL_INSURANCE");
		pricingDetails.setAmount(100);
		pricingDetailsList.add(pricingDetails);
		pricingDetails = new PricingDetails();
		pricingDetails.setKey("TOTAL_AMOUNT");
		pricingDetails.setAmount(10000);
		pricingDetailsList.add(pricingDetails);
		TotalPricing totalPricing = new TotalPricing();
		totalPricing.setDetails(pricingDetailsList);

		fullPaymentResponse = commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(noCostEmiDetailsList, totalPricing, null, null);
		Assert.assertNull(fullPaymentResponse);

		com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline = new com.mmt.hotels.model.response.pricing.CancellationTimeline();
		cancellationTimeline.setCancellationDate("00 jan");
		com.mmt.hotels.model.persuasion.response.PersuasionData persuasionData = new com.mmt.hotels.model.persuasion.response.PersuasionData();
		persuasionData.setText("No-Cost EMI available from 3 months at 5,000 per month");
		fullPaymentResponse = commonResponseTransformer.buildNoCostEmiDetailAndUpdateFullPayment(noCostEmiDetailsList, totalPricing, null, null);
		Assert.assertNull(fullPaymentResponse);
	}

	@Test
	public void buildSMESubscriptionBreakupTest() {

		List<PricingDetails> prideDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBreakDown  = new DisplayPriceBreakDown();
		displayPriceBreakDown.setSmeSubscriptionAmount(10.0);

		when(polyglotService.getTranslatedData(anyString())).thenReturn("abc");
		commonResponseTransformer.buildSMESubscriptionBreakup(prideDetails,displayPriceBreakDown);

		Assert.assertNotNull(prideDetails);
		Assert.assertNotNull(prideDetails.get(0));
		Assert.assertTrue(prideDetails.get(0).getAmount() == 10.0);
	}

	@Test
	public void removePlaceHolderPersuasionsForSectionTest() throws JsonParseException {
		Hotel hotel1 = new Hotel();
		String sectionName = "section";
		//Mockito.when(objectMapperUtil.getObjectFromJsonWithType(Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn(new HashMap<>());
		commonResponseTransformer.removePlaceHolderPersuasionsForSection(hotel1,sectionName);
	}

	@Test
	public void prepareUpgradeInfoTest() {
		//Null checks
		RateplansUpgrade rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(new HotelRates(), null, 0, new BlackBenefits());
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().size(), 0);

		TotalPricing totalPricing = new TotalPricing();
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(new HotelRates(), totalPricing, 0, new BlackBenefits());
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().size(), 0);

		polyglotService.getTranslatedData(PER_NIGHT_TEXT);

		//Pricing Tests
		totalPricing.setNoCouponText("NO_COUPON_TEXT");
		totalPricing.setCouponSubtext("COUPON_SUB_TEXT");
		totalPricing.setCurrency("INR");
		totalPricing.setCouponAmount(100.0);
		totalPricing.setGroupPriceText("GROUP_PRICE_TEXT");
		totalPricing.setSavingsText("SAVING_TEXT");
		totalPricing.setDetails(new ArrayList<>());
		PricingDetails taxPricingDetails = new PricingDetails();
		taxPricingDetails.setKey(TAXES_KEY);
		taxPricingDetails.setAmount(90.0);
		PricingDetails baseFarePricingDetails = new PricingDetails();
		baseFarePricingDetails.setKey(BASE_FARE_KEY);
		baseFarePricingDetails.setAmount(2000.0);
		totalPricing.getDetails().add(taxPricingDetails);
		totalPricing.getDetails().add(baseFarePricingDetails);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(new HotelRates(), totalPricing, 0, new BlackBenefits());
		Assert.assertNotNull(rateplansUpgrade);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertNotNull(rateplansUpgrade.getSelectedRateplans());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().size(), 1);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0));
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getNoCouponText(), "NO_COUPON_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getCouponSubtext(), "COUPON_SUB_TEXT");
		Assert.assertTrue(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getCouponAmount() == 100.0);
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getGroupPriceText(), "GROUP_PRICE_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getSavingsText(), "SAVING_TEXT");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getDetails().size(), 3);

		PricingDetails discountedPricingDetails = new PricingDetails();
		discountedPricingDetails.setKey(PRICE_AFTER_DISCOUNT_KEY);
		discountedPricingDetails.setAmount(2000.0);
		totalPricing.getDetails().add(discountedPricingDetails);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(new HotelRates(), totalPricing, 0, new BlackBenefits());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getPriceMap().getDetails().size(), 3);


		//Room Info Tests
		HotelRates hotelRates = new HotelRates();
		UpgradeInfo upgradeInfo = new UpgradeInfo();
		RoomInfo selectedRoomInfo = new RoomInfo();
		selectedRoomInfo.setRoomCode("100");
		selectedRoomInfo.setRoomSize("100");
		selectedRoomInfo.setRoomSizeUnit("sq.ft");
		selectedRoomInfo.setBedType("Double Bed");
		selectedRoomInfo.setBeds(new ArrayList<>());
		selectedRoomInfo.getBeds().add(new SleepingArrangement());
		selectedRoomInfo.getBeds().get(0).setType("Double Bed");
		selectedRoomInfo.setRoomViewName("City View");
		selectedRoomInfo.setRoomName("ECONOMIC ROOM");
		upgradeInfo.setRoomInfo(selectedRoomInfo);
		hotelRates.setUpgradeInfo(upgradeInfo);
		RoomInfo upgradedRoomInfo = new RoomInfo();
		upgradedRoomInfo.setRoomCode("101");
		upgradedRoomInfo.setRoomSize("120");
		upgradedRoomInfo.setRoomSizeUnit("sq.ft");
		upgradedRoomInfo.setBedType("King Bed");
		upgradedRoomInfo.setBeds(new ArrayList<>());
		upgradedRoomInfo.getBeds().add(new SleepingArrangement());
		upgradedRoomInfo.getBeds().get(0).setType("King Bed");
		upgradedRoomInfo.setRoomViewName("Lake View");
		upgradedRoomInfo.setRoomName("DELUXE ROOM");
		hotelRates.setRoomInfo(new HashMap<>());
		hotelRates.getRoomInfo().put("test_room", upgradedRoomInfo);
		hotelRates.setRoomTypeDetails(new RoomTypeDetails());
		RoomType roomType = new RoomType();
		roomType.setRoomTypeCode("101");
		hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
		hotelRates.getRoomTypeDetails().getRoomType().put("101", roomType);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits());
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getRoomName(), "DELUXE ROOM");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getRoomDesc(), "120 sq.ft | King Bed | Lake View");
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().get(0).getRoomName(), "ECONOMIC ROOM");
		Assert.assertEquals(rateplansUpgrade.getSelectedRateplans().get(0).getRoomDesc(), "100 sq.ft | Double Bed | City View");

		//Inclusion Tests
		Inclusion inclusion = new Inclusion();
		inclusion.setCode("300");
		inclusion.setValue("Room Upgraded");
		inclusion.setIconUrl("Test Icon");
		upgradeInfo.setInclusions(new ArrayList<>());
		upgradeInfo.getInclusions().add(inclusion);
		rateplansUpgrade = commonResponseTransformer.prepareUpgradeInfo(hotelRates, totalPricing, 0, new BlackBenefits());
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans());
		Assert.assertTrue(rateplansUpgrade.getUpgradedRateplans().size() > 0);
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList());
		Assert.assertNotNull(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0));
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0).getText(), "Room Upgraded");
		Assert.assertEquals(rateplansUpgrade.getUpgradedRateplans().get(0).getInclusionsList().get(0).getIconUrl(), "Test Icon");
	}

	@Test
	public void prepareTagInfoTest() {
		when(polyglotService.getTranslatedData(ROOM_UPGRADED_TAG_TITLE)).thenReturn("Room Upgraded");
		TagInfo tagInfo = commonResponseTransformer.prepareTagInfo(null);
		Assert.assertNull(tagInfo);
		tagInfo = commonResponseTransformer.prepareTagInfo(new BlackBenefits());
		Assert.assertNull(tagInfo);
		BlackBenefits blackBenefits = new BlackBenefits();
		blackBenefits.setRoomUpgrade(true);
		tagInfo = commonResponseTransformer.prepareTagInfo(blackBenefits);
		Assert.assertNotNull(tagInfo);
		Assert.assertNotNull(tagInfo.getTitle());
		Assert.assertEquals(tagInfo.getTitle(), "Room Upgraded");
	}



	@Test
	public void buildNewPropertyOfferCouponPersuasionTest() {
		// Arrange
		when(polyglotService.getTranslatedData(PROPERTY_DISCOUNT_PERSUASION_DESC_APPS)).thenReturn("<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR {DISCOUNT_AMT} Off. Applicable on First 5 Bookings at this property</font>");

		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);

		// Test case: discountPersuasionInfo is null
		CouponPersuasion couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildNewPropertyOfferCouponPersuasion", (Object) null);
		Assert.assertNull(couponPersuasion);

		// Test case: but discount is 0.0
		DiscountPersuasionInfo discountPersuasionInfo = new DiscountPersuasionInfo();
		discountPersuasionInfo.setDiscount(0.0);
		discountPersuasionInfo.setBookingCount(5);
		couponPersuasion = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildNewPropertyOfferCouponPersuasion",
				discountPersuasionInfo
		);
		Assert.assertNull(couponPersuasion);

		// Test case: bookingCount is 0
		discountPersuasionInfo.setDiscount(100.0);
		discountPersuasionInfo.setBookingCount(0);
		couponPersuasion = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildNewPropertyOfferCouponPersuasion",
				discountPersuasionInfo
		);
		Assert.assertNull(couponPersuasion);

		// Test case: valid discountPersuasionInfo with apps client
		discountPersuasionInfo.setDiscount(100.0);
		discountPersuasionInfo.setBookingCount(5);
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), DEVICE_IOS);
		couponPersuasion = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildNewPropertyOfferCouponPersuasion",
				discountPersuasionInfo
		);
		Assert.assertNotNull(couponPersuasion);
		Assert.assertEquals(couponPersuasion.getText(), "<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR 100 Off. Applicable on First 5 Bookings at this property</font>");
		Assert.assertNull(couponPersuasion.getIconType());
		Assert.assertNull(couponPersuasion.getHeading());

		// Test case: valid discountPersuasionInfo with desktop client
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
		couponPersuasion = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildNewPropertyOfferCouponPersuasion",
				discountPersuasionInfo
		);
		Assert.assertNotNull(couponPersuasion);
		Assert.assertEquals(couponPersuasion.getText(), "<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR 100 Off. Applicable on First 5 Bookings at this property</font>");

		// Test case: isNewPropertyOfferApplicable is false
		couponPersuasion = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer,
				"buildNewPropertyOfferCouponPersuasion",
				discountPersuasionInfo
		);
		Assert.assertNotNull(couponPersuasion);
	}

	@Test
	public void buildFirstFiveOfferPersuasionTest(){

		when(polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_DESC_APPS)).thenReturn("<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR {DISCOUNT_AMT} Off. Applicable on First 5 Bookings at this property</font>");
		when(polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_DESC_DESKTOP)).thenReturn("Get FLAT {DISCOUNT_AMT} Off. Applicable on First 5 Bookings at this property");
		when(polyglotService.getTranslatedData(FIRST_FIVE_BOOKING_HEADING)).thenReturn("Additional 15% Discount");
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.REVIEW_AVAIL_ROOMS);
		CouponPersuasion couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", "FIRST_FIVE_BOOKING",null);
		Assert.assertNull(couponPersuasion);
		couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", null,100.0);
		Assert.assertNull(couponPersuasion);
		couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", null,null);
		Assert.assertNull(couponPersuasion);
		couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", "FIRST_FIVE_BOOKING",100.0);
		Assert.assertNotNull(couponPersuasion);
		Assert.assertEquals(couponPersuasion.getText(),"<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR 100 Off. Applicable on First 5 Bookings at this property</font>");
		Assert.assertNull(couponPersuasion.getIconType());
		Assert.assertNull(couponPersuasion.getHeading());
		MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), ControllerConstants.DETAIL_SEARCH_ROOMS);
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), CLIENT_DESKTOP);
		couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", "FIRST_FIVE_BOOKING",100.0);
		Assert.assertNotNull(couponPersuasion);
		Assert.assertEquals(couponPersuasion.getIconType(),Constants.FIRST_FIVE_BOOKING_ICON_TYPE);
		Assert.assertEquals(couponPersuasion.getText(),"Get FLAT 100 Off. Applicable on First 5 Bookings at this property");
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), DEVICE_IOS);
		couponPersuasion = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildFirstFiveBookingCouponPersuasion", "FIRST_FIVE_BOOKING",100.0);
		Assert.assertNotNull(couponPersuasion);
		Assert.assertEquals(couponPersuasion.getText(),"<font color='#007E7D'><b>Limited Offer:</b> Get FLAT INR 100 Off. Applicable on First 5 Bookings at this property</font>");

	}


	@Test
	public void buildUpsellRateplanInfoTest() {
		UpsellRateplanInfo hesUpsellRateplanInfo = new UpsellRateplanInfo();
		hesUpsellRateplanInfo.setBaseRateplanCode("rpc123");
		hesUpsellRateplanInfo.setUpsellType("Room");
		hesUpsellRateplanInfo.setBaseRoomCode("1234");
		hesUpsellRateplanInfo.setUpsellRatePlanPriceDiff(56);
		hesUpsellRateplanInfo.setBaseRoomName("King Bed");
		com.mmt.hotels.clientgateway.response.searchHotels.UpsellRateplanInfo result = commonResponseTransformer.buildUpsellRateplanInfo(hesUpsellRateplanInfo,"INR");
		Assert.assertNotNull(result);
	}

	@Test
	public void buildVideoTest(){
		MatchMakerVideoInfo videoInfo = new MatchMakerVideoInfo();
		videoInfo.setUrl("url");
		videoInfo.setThumbnailUrl("thumbnail");
		Video video = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildVideo", videoInfo);
		Assert.assertNotNull(video);
		Assert.assertEquals("url",video.getUrl());
		Assert.assertNotNull("thumbnail",video.getThumbnailUrl());
	}

	@Test
	public void buildisSupplierExcludedTest(){
		String req = "";
		boolean res = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "isSupplierExcluded", req);
		Assert.assertFalse(res);
		req = "abc";
		res = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "isSupplierExcluded", req);
		Assert.assertTrue(res);
		req = "xyz";
		res = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "isSupplierExcluded", req);
		Assert.assertFalse(res);

	}
	@Test
	public void buildHotelFundedDiscountTest(){
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildHotelFundedDiscount", pricingDetails,displayPriceBrkDwn);
		Assert.assertEquals(0,pricingDetails.size());
		displayPriceBrkDwn.setEffectiveDiscount(100.0);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildHotelFundedDiscount", pricingDetails,displayPriceBrkDwn);
		Assert.assertEquals(1,pricingDetails.size());

	}

	@Test
	public void buildTdsTest(){
		List<PricingDetails> pricingDetails = new ArrayList<>();
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTDS", pricingDetails,displayPriceBrkDwn);
		Assert.assertEquals(0,pricingDetails.size());
		displayPriceBrkDwn.setTdsAmount(100.0);
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildTDS", pricingDetails,displayPriceBrkDwn);
		Assert.assertEquals(1,pricingDetails.size());

	}


	@Test
	public void getGroupPriceTextForGRPNTForAltAccoTest(){
		String res = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(null);
		Assert.assertTrue(StringUtils.isEmpty(res));
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO)).thenReturn("{NIGHT_COUNT} Night");
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO)).thenReturn("{NIGHT_COUNT} Nights");
		res = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(1);
		Assert.assertEquals("1 Night",res);
		res = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(3);
		Assert.assertEquals("3 Nights",res);

	}

	@Test
	public void shouldReturnNullWhenMainImagesAndVideoInfosAreEmpty() {
		List<MediaInfo> result = commonResponseTransformer.buildMedia(Collections.emptyList(), Collections.emptyList(), new HashMap<>(), false);

		assertNull(result);
	}

	@Test
	public void shouldReturnMediaInfoListWhenMainImagesAreNotEmpty() {
		List<String> mainImages = Arrays.asList("image1", "image2", "image3");

		List<MediaInfo> result = commonResponseTransformer.buildMedia(mainImages, Collections.emptyList(), new HashMap<>(), false);

		assertNotNull(result);
		assertEquals(3, result.size());
		assertEquals("IMAGE", result.get(0).getMediaType());
		assertEquals("image1", result.get(0).getUrl());
	}

	@Test
	public void shouldReturnMediaInfoListWhenVideoInfosAreNotEmpty() {
		List<VideoInfo> videoInfos = new ArrayList<>();
		VideoInfo videoInfo = new VideoInfo();
		videoInfo.setUrl("video1");
		videoInfo.setThumbnailUrl("thumbnail1");
		videoInfos.add(videoInfo);

		List<MediaInfo> result = commonResponseTransformer.buildMedia(Collections.emptyList(), videoInfos, new HashMap<>(), false);

		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("VIDEO", result.get(0).getMediaType());
		assertEquals("video1", result.get(0).getUrl());
	}

	@Test
	public void shouldLimitMediaInfoListSizeToMediaLimit() {
		List<String> mainImages = Arrays.asList("image1", "image2", "image3", "image4", "image5", "image6");

		List<MediaInfo> result = commonResponseTransformer.buildMedia(mainImages, Collections.emptyList(), new HashMap<>(), false);

		assertNotNull(result);
		assertEquals(5, result.size());
	}
	@Test
	public void shouldReturnNullWhenPoiListIsEmpty() {
		List<Poi> result = commonResponseTransformer.getPois(Collections.emptyList());

		assertNull(result);
	}

	@Test
	public void shouldReturnPoiListWhenPoiListIsNotEmpty() {
		POIInfo poiInfo = new POIInfo();
		poiInfo.setId("1234");
		poiInfo.setCentre(new Centre());
		poiInfo.setMeta(new Meta());
		poiInfo.getMeta().setCategory(new Category());
		poiInfo.getMeta().getCategory().setName("Airport");
		List<POIInfo> poiInfoList = Arrays.asList(poiInfo);

//		when(poiInfo.getCentre()).thenReturn(new Centre());
//		when(poiInfo.getMeta()).thenReturn(meta);
//		when(meta.getCategory()).thenReturn(category);
//		when(category.getName()).thenReturn("categoryName");

		List<Poi> result = commonResponseTransformer.getPois(poiInfoList);

		assertNotNull(result);
		assertEquals(1, result.size());
//		assertEquals("categoryName", result.get(0).getMeta().getCategory());
	}

	@Test
	public void shouldReturnPoiListWithoutCategoryWhenCategoryNameIsBlank() {
		POIInfo poiInfo = new POIInfo();
		poiInfo.setId("1234");
		poiInfo.setCentre(new Centre());
		poiInfo.setMeta(new Meta());
		poiInfo.getMeta().setCategory(new Category());
		poiInfo.getMeta().getCategory().setName("Airport");
		List<POIInfo> poiInfoList = Arrays.asList(poiInfo);

//		when(poiInfo.getCentre()).thenReturn(new Centre());
//		when(poiInfo.getMeta()).thenReturn(meta);
//		when(meta.getCategory()).thenReturn(category);
//		when(category.getName()).thenReturn("");

		List<Poi> result = commonResponseTransformer.getPois(poiInfoList);

		assertNotNull(result);
		assertEquals(1, result.size());
//		assertNull(result.get(0).getMeta().getCategory());
	}

	@Test
	public void shouldReturnPoiListWithoutMetaWhenMetaIsNull() {
		POIInfo poiInfo = new POIInfo();
		poiInfo.setId("1234");
		poiInfo.setCentre(new Centre());
		poiInfo.setMeta(new Meta());
		poiInfo.getMeta().setCategory(new Category());
		poiInfo.getMeta().getCategory().setName("Airport");
		List<POIInfo> poiInfoList = Arrays.asList(poiInfo);

//		when(poiInfo.getCentre()).thenReturn(new Centre());
//		when(poiInfo.getMeta()).thenReturn(null);

		List<Poi> result = commonResponseTransformer.getPois(poiInfoList);

		assertNotNull(result);
		assertEquals(1, result.size());
//		assertNull(result.get(0).getMeta());
	}

	@Test
	public void buildHighDemandHotelTagTest(){
		AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
		availRoomsResponse.setHotelInfo(new com.mmt.hotels.clientgateway.response.staticdetail.HotelResult());
		when(polyglotService.getTranslatedData(HIGH_DEMAND_PERSUASION)).thenReturn("High Demand");
		ReflectionTestUtils.setField(commonResponseTransformer, "highDemandBackgroundColor", "Black");
		ReflectionTestUtils.setField(commonResponseTransformer, "highDemandTitleColor", "red");
		commonResponseTransformer.buildHighDemandHotelTag(availRoomsResponse);
		Assert.assertNotNull(availRoomsResponse.getHotelInfo().getHotelTags());
		Assert.assertTrue(availRoomsResponse.getHotelInfo().getHotelTags().containsKey(PLACEHOLDER_PC_HOTEL_TOP));
		Assert.assertEquals("High Demand",availRoomsResponse.getHotelInfo().getHotelTags().get(PLACEHOLDER_PC_HOTEL_TOP).getTitle());
		Assert.assertEquals("Black",availRoomsResponse.getHotelInfo().getHotelTags().get(PLACEHOLDER_PC_HOTEL_TOP).getBackground());
		Assert.assertEquals("red",availRoomsResponse.getHotelInfo().getHotelTags().get(PLACEHOLDER_PC_HOTEL_TOP).getTitleColor());
	}

	@Test
	public void testAddBusinessIdentificationCard_zeroSavingPerc() {
		businessIdentificationCards.add(new com.mmt.hotels.clientgateway.response.moblanding.CardData());
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.addBusinessIdentificationCard(0);
		assertNull(result);
	}

	@Test
	public void testAddBusinessIdentificationCard_nullCardData() {
		businessIdentificationCards.add(null);
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.addBusinessIdentificationCard(10);
		assertEquals(1, result.size());
	}

	@Test
	public void testAddBusinessIdentificationCard_validInput() {
		when(utility.isReviewPageAPI(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()))).thenReturn(false);
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.addBusinessIdentificationCard(10);
		assertEquals(1, result.size());
		assertEquals("10% cheaper than the regular rates", result.get(0).getCardInfo().getSubText());
	}

	@Test
	public void testBuildBusinessIdentificationCards_emptyAffiliateId() {
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.buildBusinessIdentificationCards(new HotelRates(), "");
		assertNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCards_nullHotelRates() {
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.buildBusinessIdentificationCards(null, "affiliateId");
		assertNull(result);
	}

	@Test
	public void testBuildBusinessIdentificationCards_validInput() {
		HotelRates hotelRates = new HotelRates();
		RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
		RoomType roomType = new RoomType();
		com.mmt.hotels.model.response.pricing.RatePlan ratePlan = new com.mmt.hotels.model.response.pricing.RatePlan();
		DisplayFare displayFare = new DisplayFare();
		DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
		displayPriceBreakDown.setSavingPerc(10.0);
		displayFare.setDisplayPriceBreakDown(displayPriceBreakDown);
		ratePlan.setDisplayFare(displayFare);
		if(MapUtils.isEmpty(roomType.getRatePlanList())){
			roomType.setRatePlanList(new HashMap<>());
		}
		roomType.getRatePlanList().put("ratePlan", ratePlan);
		if(MapUtils.isEmpty(roomTypeDetails.getRoomType())){
			roomTypeDetails.setRoomType(new HashMap<>());
		}
		roomTypeDetails.getRoomType().put("roomType", roomType);
		hotelRates.setRoomTypeDetails(roomTypeDetails);
		when(utility.isDetailPageAPI(MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue()))).thenReturn(true);
		when(utility.isBusinessIdentificationApplicable(anyString(), Mockito.any())).thenReturn(true);
		List<com.mmt.hotels.clientgateway.response.moblanding.CardData> result = commonResponseTransformer.buildBusinessIdentificationCards(hotelRates, "affiliateId");
		assertEquals(1, result.size());
	}

	@Test
	public void testUpdatePriceFooterForPAHOnly_PAHOnlyPaymode() {
		String payMode = "PAH_ONLY";
		TotalPricing totalPricing = new TotalPricing();
		commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, totalPricing);
		assertNull(totalPricing.getPriceFooter());
	}
	@Test
	public void testUpdatePriceFooterForPAHOnly_PAHWithCC() {
		String payMode = "PAH_WITH_CC";
		TotalPricing totalPricing = new TotalPricing();
		when(polyglotService.getTranslatedData(Mockito.any())).thenReturn("Price Footer Text");
		commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, totalPricing);
		assertEquals("Price Footer Text", totalPricing.getPriceFooter().getText());
	}

	@Test
	public void testUpdatePriceFooterForPAHOnly_NotPAHOnlyPaymode() {
		String payMode = "NOT_PAH_ONLY";
		TotalPricing totalPricing = new TotalPricing();
		commonResponseTransformer.updatePriceFooterForPAHOnly(payMode, totalPricing);
		assertNull(totalPricing.getPriceFooter());
	}

	@Test
	public void testUpdateHotelCategories_ThemificationEnabled_NonEmptyLists() {
		List<String> categories = new ArrayList<>(Arrays.asList("Category1", "Category2"));
		Set<String> hotelCategories = new HashSet<>(Arrays.asList("Category3", "Category4"));
		commonResponseTransformer.updateHotelCategories(true, categories, hotelCategories);
		assertTrue(categories.contains("Category1"));
		assertTrue(categories.contains("Category2"));
	}

	@Test
	public void testUpdateHotelCategories_ThemificationDisabled_NonEmptyLists() {
		List<String> categories = new ArrayList<>(Arrays.asList("Category1", "Category2"));
		Set<String> hotelCategories = new HashSet<>(Arrays.asList("Category3", "Category4"));
		commonResponseTransformer.updateHotelCategories(false, categories, hotelCategories);
		assertFalse(categories.contains("Category3"));
		assertFalse(categories.contains("Category4"));
	}

	@Test
	public void testUpdateHotelCategories_ThemificationEnabled_EmptyLists() {
		List<String> categories = new ArrayList<>();
		Set<String> hotelCategories = new HashSet<>();
		commonResponseTransformer.updateHotelCategories(true, categories, hotelCategories);
		assertTrue(categories.isEmpty());
	}

	@Test
	public void testUpdateHotelCategories_ThemificationDisabled_EmptyLists() {
		List<String> categories = new ArrayList<>();
		Set<String> hotelCategories = new HashSet<>();
		commonResponseTransformer.updateHotelCategories(false, categories, hotelCategories);
		assertTrue(categories.isEmpty());
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_OneRoomOneNight() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN)).thenReturn("{NIGHT_COUNT} Night, {ROOM_COUNT} Room");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(1, 1);
		assertEquals("1 Night, 1 Room", result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_MultipleRoomsOneNight() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_NEW_DESIGN)).thenReturn("{NIGHT_COUNT} Night, {ROOM_COUNT} Rooms");
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(2, 1);
		assertEquals("1 Night, 2 Rooms", result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_OneRoomMultipleNights() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_ROOM_NEW_DESIGN)).thenReturn("{NIGHT_COUNT} Nights, {ROOM_COUNT} Room");
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(1, 2);
		assertEquals("2 Nights, 1 Room", result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_MultipleRoomsMultipleNights() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_NEW_DESIGN)).thenReturn("{NIGHT_COUNT} Nights, {ROOM_COUNT} Rooms");
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(2, 2);
		assertEquals("2 Nights, 2 Rooms", result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_NullRoomCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(null, 1);
		assertNull(result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_NullNightCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(1, null);
		assertNull(result);
	}

	@Test
	public void testGetGroupPriceTextForGRPNT_NullRoomCountAndNightCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(null, null);
		assertNull(result);
	}

	@Test
	public void buildInsuranceAddOnData_test() throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		InsuranceData dataFromHES = new InsuranceData();
		List<TmInsuranceAddOns> addonsList = new ArrayList<>();
		TmInsuranceAddOns tminsaddon = new TmInsuranceAddOns();
		addonsList.add(tminsaddon);
		dataFromHES.setTmInsuranceAddOns(addonsList);
		com.mmt.hotels.model.response.addon.WidgetData insuarnceWidgetDataHES = new WidgetData();
		String jsonString = "{\"name\":\"John\", \"age\":30, \"city\":\"New York\"}";
		JsonNode ui = mapper.readTree(jsonString);
		insuarnceWidgetDataHES.setUi(ui);
		JsonNode data = mapper.readTree(jsonString);
		insuarnceWidgetDataHES.setData(data);
		InsuranceAddOnData insuranceAddOnData = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildInsuranceAddOnData", dataFromHES, insuarnceWidgetDataHES);
		Assert.assertNotNull(insuranceAddOnData.getWidgetData().getData());
		Assert.assertNotNull(insuranceAddOnData.getWidgetData().getUi());
	}

	@Test
	public void buildInsuranceAddOnData_testFailedCase() {
		InsuranceData dataFromHES = new InsuranceData();
		List<TmInsuranceAddOns> addonsList = new ArrayList<>();
		TmInsuranceAddOns tminsaddon = new TmInsuranceAddOns();
		addonsList.add(tminsaddon);
		dataFromHES.setTmInsuranceAddOns(addonsList);
		com.mmt.hotels.model.response.addon.WidgetData insuarnceWidgetDataHES = new WidgetData();
		insuarnceWidgetDataHES.setUi(null);
		insuarnceWidgetDataHES.setData(null);
		InsuranceAddOnData insuranceAddOnData = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildInsuranceAddOnData", dataFromHES, insuarnceWidgetDataHES);
		Assert.assertNotNull(insuranceAddOnData);
		Assert.assertNull(insuranceAddOnData.getWidgetData());
	}

	@Test
	public void getOriginalPriceText_test() {
		int price = 12212;
		String askedCurrency = "INR";
		when(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE)).thenReturn("<font color='#007E7D'>Original Price: <b>{currency} {totalPrice}</b></font>");
		String result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "getOriginalPriceText", price, askedCurrency);
		Assert.assertNotNull(result);
		Assert.assertTrue(result.contains("#007E7D"));
		Assert.assertTrue(result.contains("12,212"));
	}

	@Test
	public void getOriginalPriceText_testFailedCase() {
		int price = 12212;
		String askedCurrency = "INR";
		when(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE)).thenReturn(null);
		String result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "getOriginalPriceText", price, askedCurrency);
		Assert.assertNull(result);
	}

	@Test
	public void updateOriginalPriceTextInUpgradedRateplan_test() {
		List<RatePlanDetails> upgradedRateplans = new ArrayList<>();
		RatePlanDetails upgradedRateplan = new RatePlanDetails();
		TotalPricing totalPriceMap = new TotalPricing();
		upgradedRateplan.setPriceMap(totalPriceMap);
		upgradedRateplans.add(upgradedRateplan);
		UpgradedRateplanOriginalPriceDetails upgradeRateplanOriginalPriceDetails = new UpgradedRateplanOriginalPriceDetails();
		upgradeRateplanOriginalPriceDetails.setCurrency("INR");
		upgradeRateplanOriginalPriceDetails.setDisplayPrice(43233);
		upgradeRateplanOriginalPriceDetails.setTaxes(123);
		int los = 2;
		String currency = "INR";
		when(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE)).thenReturn("<font color='#007E7D'>Original Price: <b>{currency} {totalPrice}</b></font>");
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "updateOriginalPriceTextInUpgradedRateplan",
				upgradedRateplans, upgradeRateplanOriginalPriceDetails, los, currency);
		Assert.assertNotNull(upgradedRateplans.get(0).getPriceMap().getOriginalPriceMsg());
	}

	@Test
	public void updateOriginalPriceTextInUpgradedRateplan_testFailCase() {
		List<RatePlanDetails> upgradedRateplans = new ArrayList<>();
		RatePlanDetails upgradedRateplan = new RatePlanDetails();
		TotalPricing totalPriceMap = new TotalPricing();
		upgradedRateplan.setPriceMap(totalPriceMap);
		upgradedRateplans.add(upgradedRateplan);
		UpgradedRateplanOriginalPriceDetails upgradeRateplanOriginalPriceDetails = new UpgradedRateplanOriginalPriceDetails();
		upgradeRateplanOriginalPriceDetails.setCurrency("INR");
		upgradeRateplanOriginalPriceDetails.setDisplayPrice(0);
		upgradeRateplanOriginalPriceDetails.setTaxes(0);
		int los = 2;
		String currency = "INR";
		//Mockito.when(polyglotService.getTranslatedData(UPGRADE_RATE_PLAN_ORIGINAL_PRICE)).thenReturn("<font color='#007E7D'>Original Price: <b>{currency} {totalPrice}</b></font>");
		ReflectionTestUtils.invokeMethod(commonResponseTransformer, "updateOriginalPriceTextInUpgradedRateplan",
				upgradedRateplans, upgradeRateplanOriginalPriceDetails, los, currency);
		Assert.assertNull(upgradedRateplans.get(0).getPriceMap().getOriginalPriceMsg());
	}

	@Test
	public void buildLongStayBenefitsTest(){
		LongStayBenefits longStayBenefits = new LongStayBenefits();
		longStayBenefits.setTitle("title");
		longStayBenefits.setTitleColor("red");
		longStayBenefits.setInclusionsList(new ArrayList<>());
		longStayBenefits.setCardId("card id");
		longStayBenefits.setBorderGradient(new BorderGradient());
		com.mmt.hotels.clientgateway.response.wrapper.LongStayBenefits res = commonResponseTransformer.buildLongStayBenefits(longStayBenefits);
		Assert.assertNotNull(res);
		Assert.assertNotNull(res.getBorderGradient());
		Assert.assertNotNull(res.getInclusionsList());
		Assert.assertEquals("title",res.getTitle());
		Assert.assertEquals("red",res.getTitleColor());
		Assert.assertEquals("card id",res.getCardId());
	}


	@Test
	public void testBuildHighLights() {
		// Test when input list is null
		List<AppInstallHighlight> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer,"buildHighLights",new ArrayList<>());
		assertNull(result);

		// Test when input list contains elements
		com.mmt.hotels.model.response.altaccodata.AppInstallHighlight highlight = new com.mmt.hotels.model.response.altaccodata.AppInstallHighlight();
		highlight.setText("Test Highlight");
		highlight.setIconUrl("Test Icon URL");
		List<com.mmt.hotels.model.response.altaccodata.AppInstallHighlight> highlights = Arrays.asList(highlight);

		result = ReflectionTestUtils.invokeMethod(commonResponseTransformer,"buildHighLights",highlights);
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Test Highlight", result.get(0).getText());
		assertEquals("Test Icon URL", result.get(0).getIconUrl());
	}

	@Test
	public void testSetPricePersuasionHotelBenefitInfo_withValidBenefitInfo() {
		// Arrange
		Persuasion benefitPersuasion = new Persuasion();
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);

		HotelBenefitInfo benefitInfo = new HotelBenefitInfo();
		benefitInfo.setText("Benefit Text");
		benefitInfo.setIconUrl("http://example.com/icon.png");
		benefitInfo.setBenefitType("Benefit Type");

		when(persuasionUtil.buildHotelBenefitPersuasionForReviewPage(benefitInfo)).thenReturn(benefitPersuasion);

		// Act
		commonResponseTransformer.setPricePersuasionHotelBenefitInfo(benefitInfo, totalPricing);

		// Assert
		assertNotNull(totalPricing.getPricePersuasions());
		assertEquals(benefitPersuasion, totalPricing.getPricePersuasions().get("PLACEHOLDER_PRICE_BOTTOM_M1"));
	}

	@Test
	public void testSetPricePersuasionHotelBenefitInfo_withNullBenefitInfo() {
		// Arrange
		TotalPricing totalPricing = new TotalPricing();
		List<PricingDetails> details = new ArrayList<PricingDetails>();
		PricingDetails priceDetails1 = new PricingDetails();
		priceDetails1.setAmount(2088.0);
		priceDetails1.setKey("TAXES");
		priceDetails1.setLabel("taxes & fees");

		PricingDetails priceDetails2 = new PricingDetails();
		priceDetails2.setAmount(12000.0);
		priceDetails2.setKey("TOTAL_AMOUNT");
		priceDetails2.setLabel("Total Amount to be paid");

		details.add(priceDetails1);
		details.add(priceDetails2);

		totalPricing.setDetails(details);
		totalPricing.setCouponAmount(0);

		// Act
		commonResponseTransformer.setPricePersuasionHotelBenefitInfo(null, totalPricing);

		// Assert
		assertNull(totalPricing.getPricePersuasions());
	}

	@Test
	public void testSetPricePersuasionHotelBenefitInfo_withNullTotalPricing() {
		// Act
		commonResponseTransformer.setPricePersuasionHotelBenefitInfo(benefitInfo, null);

		// Assert
		// No exception should be thrown
	}

	@Test
	public void convertAbridgedIntoSearchWrapperHotelEntity_ReturnsNull_WhenInputListIsEmpty() {
		List<SearchWrapperHotelEntity> result = commonResponseTransformer.convertAbridgedIntoSearchWrapperHotelEntity(Collections.emptyList());
		assertNull(result);
	}

	@Test
	public void convertAbridgedIntoSearchWrapperHotelEntity_ReturnsNull_WhenInputListIsNull() {
		List<SearchWrapperHotelEntity> result = commonResponseTransformer.convertAbridgedIntoSearchWrapperHotelEntity(null);
		assertNull(result);
	}


	@Test
	public void buildSafetyPersuasionList_ReturnsNull_WhenCategoriesIsEmpty() {
		List<String> categories = Collections.emptyList();
		LinkedHashMap<String, PersuasionResponse> result = commonResponseTransformer.buildSafetyPersuasionList(categories);
		assertNull(result);
	}

	@Test
	public void buildSafetyPersuasionList_ReturnsNull_WhenCategoriesIsNull() {
		List<String> categories = null;
		LinkedHashMap<String, PersuasionResponse> result = commonResponseTransformer.buildSafetyPersuasionList(categories);
		assertNull(result);
	}



	@Test
	public void buildSafetyPersuasionList_ReturnsEmptyMap_WhenMySafetyDataPolyglotIsBlank() {
		List<String> categories = Arrays.asList("category1", "category2");
		ReflectionTestUtils.setField(commonResponseTransformer, "mySafetyDataPolyglot", "");
		LinkedHashMap<String, PersuasionResponse> result = commonResponseTransformer.buildSafetyPersuasionList(categories);
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}
	@Test
	public void buildBenefitInfoTest(){
		HotelBenefits hotelBenefits = commonResponseTransformer.buildBenefitInfo(new HotelBenefitInfo());
		Assert.assertNotNull(hotelBenefits);
	}

	@Test
	public void testBuildPersuasions_withBlackInfo() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);
		List<Inclusion> inclusionList = new ArrayList<>();
		Inclusion inclusion = new Inclusion();
		inclusionList.add(inclusion);
		when(hotelRates.getBlackInfo()).thenReturn(mock(BlackInfo.class));
		when(hotelRates.getBlackInfo().getInclusionsList()).thenReturn(inclusionList);

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildPersuasions_withLongStayBenefits() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);
		List<Inclusion> inclusionList = new ArrayList<>();
		Inclusion inclusion = new Inclusion();
		inclusionList.add(inclusion);
		when(hotelRates.getLongStayBenefits()).thenReturn(mock(LongStayBenefits.class));
		when(hotelRates.getLongStayBenefits().getInclusionsList()).thenReturn(inclusionList);

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildPersuasions_withOccassionPackageRoomDetails() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);
		when(hotelRates.getOccassionPackageRoomDetails()).thenReturn(mock(RoomTypeDetails.class));
		when(hotelRates.getOccassionPackageRoomDetails().getOccassionDetails()).thenReturn(mock(OccassionDetails.class));

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildPersuasions_withPackageRoomDetails() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);
		when(hotelRates.getPackageRoomDetails()).thenReturn(mock(RoomTypeDetails.class));
		when(hotelRates.getPackageRoomDetails().getOccassionDetails()).thenReturn(mock(OccassionDetails.class));

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildPersuasions_withAppliedOffers() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);
		List<RangePrice> rangePrices = new ArrayList<>();
		RangePrice rangePrice = new RangePrice();
		rangePrices.add(rangePrice);
		when(hotelRates.getAppliedOffers()).thenReturn(rangePrices);

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNotNull(result);
	}

	@Test
	public void testBuildPersuasions_withNoPersuasions() {
		// Arrange
		HotelRates hotelRates = mock(HotelRates.class);

		// Act
		List<PersuasionObject> result = commonResponseTransformer.buildPersuasions(hotelRates, "");

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildInclusionList_withNonEmptyList() throws Exception {
		// Arrange
		List<Inclusion> inclusions = new ArrayList<>();
		Inclusion inclusion1 = new Inclusion();
		inclusion1.setValue("Inclusion 1");
		Inclusion inclusion2 = new Inclusion();
		inclusion2.setValue("Inclusion 2");
		inclusions.add(inclusion1);
		inclusions.add(inclusion2);

		// Act
		Method method = CommonResponseTransformer.class.getDeclaredMethod("buildInclusionList", List.class);
		method.setAccessible(true);
		List<String> result = (List<String>) method.invoke(commonResponseTransformer, inclusions);

		// Assert
		assertNotNull(result);
		assertEquals(2, result.size());
		assertTrue(result.contains("Inclusion 1"));
		assertTrue(result.contains("Inclusion 2"));
	}

	@Test
	public void testBuildInclusionList_withEmptyList() throws Exception {
		// Arrange
		List<Inclusion> inclusions = Collections.emptyList();

		// Act
		Method method = CommonResponseTransformer.class.getDeclaredMethod("buildInclusionList", List.class);
		method.setAccessible(true);
		List<String> result = (List<String>) method.invoke(commonResponseTransformer, inclusions);

		// Assert
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void testBuildInclusionList_withNullList() throws Exception {
		// Act
		Method method = CommonResponseTransformer.class.getDeclaredMethod("buildInclusionList", List.class);
		method.setAccessible(true);
		List<String> result = (List<String>) method.invoke(commonResponseTransformer, (Object) null);

		// Assert
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void testBuildSpecialOfferCard_withValidOccassionDetails() {
		// Arrange
		when(roomTypeDetails.getOccassionDetails()).thenReturn(occassionDetails);
		when(occassionDetails.getSpecialCardImageUrl()).thenReturn("specialCardImageUrl");
		when(occassionDetails.getHeaderImageUrl()).thenReturn("headerImageUrl");
		when(occassionDetails.getText()).thenReturn("text");
		when(occassionDetails.getBgGradient()).thenReturn(new BgGradient());

		// Act
		SpecialOfferCard result = commonResponseTransformer.buildSpecialOfferCard(roomTypeDetails);

		// Assert
		assertNotNull(result);
		assertEquals("specialCardImageUrl", result.getImageUrl());
		assertEquals("headerImageUrl", result.getHeaderImage());
		assertEquals("text", result.getText());
		assertNotNull(result.getBgGradient());
	}

	@Test
	public void testBuildSpecialOfferCard_withNullOccassionDetails() {
		// Arrange
		when(roomTypeDetails.getOccassionDetails()).thenReturn(null);

		// Act
		SpecialOfferCard result = commonResponseTransformer.buildSpecialOfferCard(roomTypeDetails);

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildSpecialOfferCard_withNullRoomTypeDetails() {
		// Act
		SpecialOfferCard result = commonResponseTransformer.buildSpecialOfferCard(null);

		// Assert
		assertNull(result);
	}


	@Test
	public void buildMediaTest() {

		MediaDetails mediaDetails = new MediaDetails();


		List<ImageDetails> mainImages = Arrays.asList(ImageDetails.builder().url("image1").build(), ImageDetails.builder().url("image2").build(), ImageDetails.builder().url("image3").build());
		List<VideoDetails> videoInfos = Arrays.asList(VideoDetails.builder().url("video1").build(), VideoDetails.builder().url("video2").build(), VideoDetails.builder().url("video3").build());

		mediaDetails.setImages(mainImages);
		mediaDetails.setVideos(videoInfos);
		List<MediaInfo> result = commonResponseTransformer.buildMedia(mediaDetails, new HashMap<>());
		assertNotNull(result);
		assertEquals(5, result.size());
		assertEquals("IMAGE", result.get(0).getMediaType());
		assertEquals("image1", result.get(0).getUrl());

		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put(ExperimentKeys.carouselImageCount.name(), "3");
		expDataMap.put("VIDEO", "2");
		MDC.put(MDCHelper.MDCKeys.CLIENT.getStringValue(), "IOS");
		result = commonResponseTransformer.buildMedia(mediaDetails, expDataMap);
		MDC.clear();
		Assert.assertNotNull(result);
	}

	@Test
	public void buildReviewSummaryV2Test() {
		ListingReviewDetails listingReviewDetails = new ListingReviewDetails();
		listingReviewDetails.setOta("OTA");
		listingReviewDetails.setSubRatings(new ArrayList<>());
		listingReviewDetails.getSubRatings().add(SubRatingsDetails.builder().name("name").rating(4.0).reviewCount(10).build());
		ReviewSummary reviewSummary = commonResponseTransformer.buildReviewSummary(listingReviewDetails, true);
		Assert.assertNotNull(reviewSummary);
	}

	@Test
	public void changeCombinedOTAWhenNotSupported() {
		ListingReviewDetails listingReviewDetails = new ListingReviewDetails();
		listingReviewDetails.setOta("MMT_BKG");
		listingReviewDetails.setSubRatings(new ArrayList<>());
		listingReviewDetails.getSubRatings().add(SubRatingsDetails.builder().name("name").rating(4.0).reviewCount(10).build());
		ReviewSummary reviewSummary = commonResponseTransformer.buildReviewSummary(listingReviewDetails, false);
		Assert.assertEquals("MMT", reviewSummary.getSource());
	}

	@Test
	public void buildGeoLocationTest() {
		GeoLocationDetails geoLocationDetails = GeoLocationDetails.builder().build();
		com.mmt.hotels.clientgateway.response.searchHotels.GeoLocation geoLocation = commonResponseTransformer.buildGeoLocation(geoLocationDetails);
		Assert.assertNotNull(geoLocation);
	}

	@Test
	public void testBuildAltDatesPersuasionAndBottomsheet_withValidData() throws ParseException, JsonProcessingException {
		// Arrange
		String jsonString = "{ \"alternateDatePriceDetails\": [ " +
				"{ \"checkIn\": \"2024-10-15\", \"checkOut\": \"2024-10-20\", \"selected\": false, \"cheaper\": true, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-16\", \"checkOut\": \"2024-10-21\", \"selected\": true, \"cheaper\": false, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-17\", \"checkOut\": \"2024-10-22\", \"selected\": false, \"cheaper\": true, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-18\", \"checkOut\": \"2024-10-23\", \"selected\": false, \"cheaper\": false, \"delta\": -20.0, \"currency\": \"USD\", \"price\": 220.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-19\", \"checkOut\": \"2024-10-24\", \"selected\": false, \"cheaper\": true, \"delta\": 10.0, \"currency\": \"USD\", \"price\": 190.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-20\", \"checkOut\": \"2024-10-25\", \"selected\": false, \"cheaper\": true, \"delta\": 40.0, \"currency\": \"USD\", \"price\": 160.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-21\", \"checkOut\": \"2024-10-26\", \"selected\": false, \"cheaper\": false, \"delta\": -30.0, \"currency\": \"USD\", \"price\": 230.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-22\", \"checkOut\": \"2024-10-27\", \"selected\": false, \"cheaper\": true, \"delta\": 60.0, \"currency\": \"USD\", \"price\": 140.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-23\", \"checkOut\": \"2024-10-28\", \"selected\": false, \"cheaper\": true, \"delta\": 20.0, \"currency\": \"USD\", \"price\": 180.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-24\", \"checkOut\": \"2024-10-29\", \"selected\": false, \"cheaper\": false, \"delta\": -50.0, \"currency\": \"USD\", \"price\": 250.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-25\", \"checkOut\": \"2024-10-30\", \"selected\": false, \"cheaper\": true, \"delta\": 20.0, \"currency\": \"USD\", \"price\": 180.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-26\", \"checkOut\": \"2024-10-31\", \"selected\": false, \"cheaper\": false, \"delta\": 50.0, \"currency\": \"USD\", \"price\": 250.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-27\", \"checkOut\": \"2024-11-01\", \"selected\": false, \"cheaper\": true, \"delta\": 10.0, \"currency\": \"USD\", \"price\": 190.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-28\", \"checkOut\": \"2024-11-02\", \"selected\": true, \"cheaper\": false, \"delta\": 0.0, \"currency\": \"USD\", \"price\": 200.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-29\", \"checkOut\": \"2024-11-03\", \"selected\": false, \"cheaper\": true, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-30\", \"checkOut\": \"2024-11-04\", \"selected\": false, \"cheaper\": false, \"delta\": 40.0, \"currency\": \"USD\", \"price\": 160.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-31\", \"checkOut\": \"2024-11-05\", \"selected\": false, \"cheaper\": true, \"delta\": 20.0, \"currency\": \"USD\", \"price\": 180.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-11-01\", \"checkOut\": \"2024-11-06\", \"selected\": false, \"cheaper\": false, \"delta\": -10.0, \"currency\": \"USD\", \"price\": 210.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-15\", \"checkOut\": \"2024-11-07\", \"selected\": false, \"cheaper\": true, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-16\", \"checkOut\": \"2024-11-08\", \"selected\": true, \"cheaper\": false, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-17\", \"checkOut\": \"2024-11-09\", \"selected\": false, \"cheaper\": true, \"delta\": 30.0, \"currency\": \"USD\", \"price\": 170.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-18\", \"checkOut\": \"2024-11-10\", \"selected\": false, \"cheaper\": false, \"delta\": -20.0, \"currency\": \"USD\", \"price\": 220.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-19\", \"checkOut\": \"2024-11-11\", \"selected\": false, \"cheaper\": true, \"delta\": 10.0, \"currency\": \"USD\", \"price\": 190.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-20\", \"checkOut\": \"2024-11-12\", \"selected\": false, \"cheaper\": true, \"delta\": 40.0, \"currency\": \"USD\", \"price\": 160.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-21\", \"checkOut\": \"2024-11-26\", \"selected\": false, \"cheaper\": false, \"delta\": -30.0, \"currency\": \"USD\", \"price\": 230.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-22\", \"checkOut\": \"2024-11-27\", \"selected\": false, \"cheaper\": true, \"delta\": 60.0, \"currency\": \"USD\", \"price\": 140.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-23\", \"checkOut\": \"2024-11-28\", \"selected\": false, \"cheaper\": true, \"delta\": 20.0, \"currency\": \"USD\", \"price\": 180.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-24\", \"checkOut\": \"2024-11-29\", \"selected\": false, \"cheaper\": false, \"delta\": -50.0, \"currency\": \"USD\", \"price\": 250.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-25\", \"checkOut\": \"2024-11-30\", \"selected\": false, \"cheaper\": true, \"delta\": 20.0, \"currency\": \"USD\", \"price\": 180.0, \"basePrice\": 200.0 }, " +
				"{ \"checkIn\": \"2024-10-26\", \"checkOut\": \"2024-11-31\", \"selected\": false, \"cheaper\": false, \"delta\": 50.0, \"currency\": \"USD\", \"price\": 250.0, \"basePrice\": 200.0 }, " +

				"{ \"checkIn\": \"2024-11-02\", \"checkOut\": \"2024-11-07\", \"selected\": false, \"cheaper\": true, \"delta\": 60.0, \"currency\": \"USD\", \"price\": 140.0, \"basePrice\": 200.0 } " +
				"] }";
		ObjectMapper objectMapper = new ObjectMapper();
		Map<String, List<AlternatePriceCard>> data = objectMapper.readValue(jsonString, new TypeReference<Map<String, List<AlternatePriceCard>>>() {});
		List<AlternatePriceCard> alternatePriceCards = data.get("alternateDatePriceDetails");

		SearchRoomsRequest searchRoomsRequest = mock(SearchRoomsRequest.class);
		SearchRoomsResponse searchRoomsResponse = new SearchRoomsResponse();
		SearchRoomsCriteria searchCriteria = mock(SearchRoomsCriteria.class);
		SearchCriteria searchRoomCriteria = mock(SearchCriteria.class);

		FeatureFlags featureFlags = mock(FeatureFlags.class);
		when(searchRoomsRequest.getSearchCriteria()).thenReturn((SearchRoomsCriteria) searchCriteria);
//		when(searchCriteria.getHotelId()).thenReturn("123");
		when(searchRoomsRequest.getFeatureFlags()).thenReturn(featureFlags);
		when(featureFlags.getAltCheaperDates()).thenReturn("bottomsheet_persuasion");
		when(searchRoomsRequest.getSearchCriteria().getHotelId()).thenReturn("123");
		when(searchRoomsRequest.getFeatureFlags().getAltCheaperDates()).thenReturn("bottomsheet_persuasion");
//		when(searchRoomsRequest.getSearchCriteria().getCheckIn()).thenReturn("2024-10-23");

		// Act
		commonResponseTransformer.buildAltDatesPersuasionAndBottomsheet(alternatePriceCards, searchRoomsRequest, null, searchRoomsResponse, searchRoomCriteria, 0, true, false);

		// Assert
		assertNotNull(searchRoomsResponse.getAltDatesBottomsheet());
		assertNotNull(searchRoomsResponse.getPersuasions());
		assertEquals(1, searchRoomsResponse.getPersuasions().size());
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_InIndia() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("in", "in");
		Assertions.assertEquals("in", result);
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_UAE() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("ae", "any");
		Assertions.assertEquals("uae", result);
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_GCC() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("sa", "any");
		Assertions.assertEquals("gcc", result);
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_GCC_IO() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("us", "sau");
		Assertions.assertEquals("gcc-io", result);
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_IN_IO() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("us", "in");
		Assertions.assertEquals("in-io", result);
	}

	@Test
	public void testGetEvarBasedOnCountryAndRegion_A2A() {
		String result = commonResponseTransformer.getEvarBasedOnCountryAndRegion("us", "fr");
		Assertions.assertEquals("a2a", result);
	}

	@Test
	public void testBuildCountryWiseData_NullInput() {
		assertNull(commonResponseTransformer.buildCountryWiseData((ListingReviewDetails) null));
	}

	@Test
	public void testBuildCountryWiseData_ReviewCountTooLow() {
		com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary = mock(com.mmt.hotels.model.response.flyfish.ReviewSummary.class);
		CountryWiseReviewCount reviewCount = mock(CountryWiseReviewCount.class);

		when(userReviewSummary.getCountryWiseReviewCount()).thenReturn(reviewCount);
		when(reviewCount.getCountry()).thenReturn(TEST_COUNTRY);
		when(reviewCount.getReviewCount()).thenReturn(2);

		assertNull(commonResponseTransformer.buildCountryWiseData(userReviewSummary));
	}

	@Test
	public void testBuildCountryWiseData_InvalidNationality() {
		com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary = mock(com.mmt.hotels.model.response.flyfish.ReviewSummary.class);
		CountryWiseReviewCount reviewCount = mock(CountryWiseReviewCount.class);

		when(userReviewSummary.getCountryWiseReviewCount()).thenReturn(reviewCount);
		when(reviewCount.getCountry()).thenReturn("InvalidCountry");
		when(reviewCount.getReviewCount()).thenReturn(15);

		assertNull(commonResponseTransformer.buildCountryWiseData(userReviewSummary));
	}

	@Test
	public void testBuildCountryWiseData_SuccessfulTransformation() {
		com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary = mock(com.mmt.hotels.model.response.flyfish.ReviewSummary.class);
		CountryWiseReviewCount reviewCount = mock(CountryWiseReviewCount.class);

		when(userReviewSummary.getCountryWiseReviewCount()).thenReturn(reviewCount);
		when(reviewCount.getCountry()).thenReturn(TEST_COUNTRY);
		when(reviewCount.getReviewCount()).thenReturn(15);

		when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT)).thenReturn(TEST_REVIEW_TEXT);
		CountryWiseReviewData result = commonResponseTransformer.buildCountryWiseData(userReviewSummary);

		assertNotNull(result);
		assertEquals("Great reviews from Indian!", result.getText());
		assertEquals(TEST_TEXT_COLOR, result.getColor());
	}

	@Test
	public void testBuildCountryWiseData_EmptyReviewText() {
		com.mmt.hotels.model.response.flyfish.ReviewSummary userReviewSummary = mock(com.mmt.hotels.model.response.flyfish.ReviewSummary.class);
		CountryWiseReviewCount reviewCount = mock(CountryWiseReviewCount.class);

		when(userReviewSummary.getCountryWiseReviewCount()).thenReturn(reviewCount);
		when(reviewCount.getCountry()).thenReturn(TEST_COUNTRY);
		when(reviewCount.getReviewCount()).thenReturn(15);

		when(polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT)).thenReturn("");

		CountryWiseReviewData result = commonResponseTransformer.buildCountryWiseData(userReviewSummary);

		assertNull(result);
	}

	@Test
	public void testBuildTrackingMap_NullInput() {
		CommonResponseTransformer transformer = new CommonResponseTransformer();
		Map<String, String> result = transformer.buildTrackingMap(null);
		Assertions.assertNotNull(result);
		Assertions.assertTrue(result.isEmpty());
	}

	@Test
	public void testBuildTrackingMap_EmptyInput() {
		CommonResponseTransformer transformer = new CommonResponseTransformer();
		Map<String, String> result = transformer.buildTrackingMap(new HashMap<>());
		Assertions.assertNotNull(result);
		Assertions.assertTrue(result.isEmpty());
	}

	@Test
	public void testBuildTrackingMap_ValidInput() {
		CommonResponseTransformer transformer = new CommonResponseTransformer();
		Map<String, String> trackingMap = new HashMap<>();
		trackingMap.put("key1", "value1");
		trackingMap.put("key2", "value2");

		Map<String, String> result = transformer.buildTrackingMap(trackingMap);

		Assertions.assertNotNull(result);
		Assertions.assertEquals(2, result.size());
		Assertions.assertEquals("value1", result.get("key1"));
		Assertions.assertEquals("value2", result.get("key2"));
	}

	@Test
	public void testBuildTrackingMap_ExceedThreshold() {
		CommonResponseTransformer transformer = new CommonResponseTransformer();
		Map<String, String> trackingMap = new HashMap<>();
		for (int i = 1; i <= 10; i++) {
			trackingMap.put("key" + i, "value" + i);
		}

		Map<String, String> result = transformer.buildTrackingMap(trackingMap);

		Assertions.assertNotNull(result);
		Assertions.assertTrue(result.size() <= TRACKING_MAP_THRESHOLD+1);
	}

	@Test
	public void testBuildTrackingMap_EmptyValues() {
		CommonResponseTransformer transformer = new CommonResponseTransformer();
		Map<String, String> trackingMap = new HashMap<>();
		trackingMap.put("key1", "value1");
		trackingMap.put("key2", "");
		trackingMap.put("key3", null);

		Map<String, String> result = transformer.buildTrackingMap(trackingMap);

		Assertions.assertNotNull(result);
		Assertions.assertEquals(1, result.size());
		Assertions.assertEquals("value1", result.get("key1"));
	}

	@Test
	public void testBuildPixelUrl_AllFieldsPopulated() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		data.setCityName("New York");
		data.setHotelName("Hotel ABC");
		data.setCheckIn("2023-10-01");
		data.setCheckOut("2023-10-05");
		data.setGuestCount(2);
		data.setUserCountry("US");
		data.setDeviceId("device123");
		data.setJourneyId("journey456");
		String pixelBaseUrl = "https://example.com/pixel";
		String expectedUrl = pixelBaseUrl +
				"u6=USD;" +
				"u19=USD;" +
				"u7=en;" +
				"u20=" + URLEncoder.encode("New York", "UTF-8").replace("+", "%20") + ";" +
				"u21=" + URLEncoder.encode("Hotel ABC", "UTF-8").replace("+", "%20") + ";" +
				"u22=" + Utility.formatDateForPixelUrl("2023-10-01") + ";" +
				"u23=" + Utility.formatDateForPixelUrl("2023-10-05") + ";" +
				"u24=2;" +
				"u30=US;" +
				"dc_rdid=device123;" +
				"ord=journey456";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(expectedUrl, result);
	}

	@Test
	public void testBuildPixelUrl_MissingOptionalFields() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		data.setUserCountry("US");
		String pixelBaseUrl = "https://example.com/pixel?";
		String expectedUrl = pixelBaseUrl +
				"u6=USD;" +
				"u19=USD;" +
				"u7=en;" +
				"u30=US;";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(expectedUrl, result);
	}

	@Test
	public void testBuildPixelUrl_EmptyData() {
		PixelUrlConfig data = new PixelUrlConfig();
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl, result);
	}

	@Test
	public void testBuildPixelUrl_NullData() {
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(null);
		Assertions.assertNull(result);
	}

	@Test
	public void testBuildPixelUrl_SpecialCharacters() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCityName("New York & Co.");
		data.setHotelName("Hotel @ ABC");
		String pixelBaseUrl = "https://example.com/pixel?";
		String expectedUrl = pixelBaseUrl +
				"u20=" + URLEncoder.encode("New York & Co.", "UTF-8").replace("+", "%20") + ";" +
				"u21=" + URLEncoder.encode("Hotel @ ABC", "UTF-8").replace("+", "%20") + ";";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(expectedUrl, result);
	}

	@Test
	public void testBuildPixelUrl_PartialData() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("EUR");
		data.setCheckIn("2023-12-01");
		String pixelBaseUrl = "https://example.com/pixel?";
		String expectedUrl = pixelBaseUrl +
				"u6=EUR;" +
				"u19=EUR;" +
				"u22=" + Utility.formatDateForPixelUrl("2023-12-01") + ";";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(expectedUrl, result);
	}

	@Test
	public void testBuildPixelUrl_NullCurrency() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setLanguage("en");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl + "u7=en;", result);
	}

	@Test
	public void testBuildPixelUrl_NullLanguage() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl + "u6=USD;u19=USD;", result);
	}

	@Test
	public void testBuildPixelUrl_NullCityName() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl + "u6=USD;u19=USD;u7=en;", result);
	}

	@Test
	public void testBuildPixelUrl_NullHotelName() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		data.setCityName("New York");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u20=New%20York;"));
		Assertions.assertFalse(result.contains("u21="));
	}

	@Test
	public void testBuildPixelUrl_NullCheckIn() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		data.setCityName("New York");
		data.setHotelName("Hotel ABC");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u21=Hotel%20ABC;"));
		Assertions.assertFalse(result.contains("u22="));
	}

	@Test
	public void testBuildPixelUrl_NullCheckOut() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("USD");
		data.setLanguage("en");
		data.setCityName("New York");
		data.setHotelName("Hotel ABC");
		data.setCheckIn("2023-10-01");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u22=01/10/23;"));
		Assertions.assertFalse(result.contains("u23="));
	}

	@Test
	public void testBuildPixelUrl_OnlyCurrency() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCurrency("EUR");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl + "u6=EUR;u19=EUR;", result);
	}

	@Test
	public void testBuildPixelUrl_OnlyLanguage() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setLanguage("fr");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl + "u7=fr;", result);
	}

	@Test
	public void testBuildPixelUrl_OnlyCityName() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCityName("Paris");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u20=Paris;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyHotelName() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setHotelName("Hotel XYZ");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u21=Hotel%20XYZ;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyCheckInDate() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCheckIn("2023-11-01");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u22=01/11/23;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyCheckOutDate() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCheckOut("2023-11-05");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u23=05/11/23;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyGuestCount() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setGuestCount(3);
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u24=3;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyDeviceId() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setDeviceId("device456");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("dc_rdid=device456;"));
	}

	@Test
	public void testBuildPixelUrl_OnlyJourneyId() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setJourneyId("journey789");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("ord=journey789"));
	}

	@Test
	public void testBuildPixelUrl_EmptyCityName() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCityName("");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertFalse(result.contains("u20="));
	}

	@Test
	public void testBuildPixelUrl_EmptyHotelName() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setHotelName("");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertFalse(result.contains("u21="));
	}

	@Test
	public void testBuildPixelUrl_EmptyDeviceId() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setDeviceId("");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertFalse(result.contains("dc_rdid="));
	}

	@Test
	public void testBuildPixelUrl_EmptyJourneyId() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setJourneyId("");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertFalse(result.contains("ord="));
	}

	@Test
	public void testBuildPixelUrl_AllFieldsEmpty() {
		PixelUrlConfig data = new PixelUrlConfig();
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertEquals(pixelBaseUrl, result);
	}

	@Test
	public void testBuildPixelUrl_OnlyUserCountry() {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setUserCountry("IN");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u30=IN;"));
	}

	@Test
	public void testBuildPixelUrl_OnlySpecialCharactersInCityName() throws Exception {
		PixelUrlConfig data = new PixelUrlConfig();
		data.setCityName("!@#$%^&*()");
		String pixelBaseUrl = "https://example.com/pixel?";
		ReflectionTestUtils.setField(commonResponseTransformer, "pixelBaseUrl", pixelBaseUrl);
		String result = commonResponseTransformer.buildPixelUrl(data);
		Assertions.assertTrue(result.contains("u20=" + URLEncoder.encode("!@#$%^&*()", "UTF-8").replace("+", "%20") + ";"));
	}


	@Test
	public void processFacilities_WithEmptyFacilitiesList_ShouldReturnEmptyList() {
		// Arrange
		List<Facility> facilities = new ArrayList<>();
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void processFacilities_WithNullFacilitiesList_ShouldReturnEmptyList() {
		// Arrange
		List<Facility> facilities = null;
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void processFacilities_WithBasicFacility_ShouldSetNameIconAndStrikeThrough() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Air Conditioning");
		facility.setIconUrl("icon_url");

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = true;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Air Conditioning", result.get(0).getName());
		assertEquals("icon_url", result.get(0).getIconUrl());
		assertTrue(result.get(0).isStrikeThrough());
		assertNull(result.get(0).getSubText());
	}

	@Test
	public void processFacilities_WithDisplayType1_ShouldAddCommaSeparatedChildAttributesInBraces() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Bathroom");
		facility.setIconUrl("bathroom_icon");
		facility.setDisplayType("1");

		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility attr1 = new AttributesFacility();
		attr1.setName("Shower");
		AttributesFacility attr2 = new AttributesFacility();
		attr2.setName("Bathtub");
		childAttributes.add(attr1);
		childAttributes.add(attr2);
		facility.setChildAttributes(childAttributes);
		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Bathroom", result.get(0).getName());
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Shower,Bathtub" + Constants.AMENITIES_CLOSING_BRACE,
				result.get(0).getSubText());
		assertFalse(result.get(0).isStrikeThrough());
	}

	@Test
	public void processFacilities_WithDisplayType2_ShouldAddFormattedChildAndSubAttributesInBraces() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Kitchen");
		facility.setIconUrl("kitchen_icon");
		facility.setDisplayType("2");

		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility childAttr = new AttributesFacility();
		childAttr.setName("Cookware");

		List<com.mmt.model.SubAttributeFacility> subAttributes = new ArrayList<>();
		com.mmt.model.SubAttributeFacility sub1 = new com.mmt.model.SubAttributeFacility();
		sub1.setName("Pots");
		com.mmt.model.SubAttributeFacility sub2 = new com.mmt.model.SubAttributeFacility();
		sub2.setName("Pans");
		subAttributes.add(sub1);
		subAttributes.add(sub2);

		childAttr.setSubAttributes(subAttributes);
		childAttributes.add(childAttr);
		facility.setChildAttributes(childAttributes);

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Kitchen", result.get(0).getName());
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Cookware " + Constants.HYPEN + " Pots,Pans" + Constants.AMENITIES_CLOSING_BRACE,
				result.get(0).getSubText());
		assertFalse(result.get(0).isStrikeThrough());
	}

	@Test
	public void processFacilities_WithDisplayType2AndNoSubAttributes_ShouldOnlyIncludeChildAttribute() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Kitchen");
		facility.setIconUrl("kitchen_icon");
		facility.setDisplayType("2");

		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility childAttr = new AttributesFacility();
		childAttr.setName("Cookware");
		// No sub-attributes

		childAttributes.add(childAttr);
		facility.setChildAttributes(childAttributes);

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Kitchen", result.get(0).getName());
		// Only contains Cookware - with no trailing comma
		assertTrue(result.get(0).getSubText().contains("Cookware " + Constants.HYPEN));
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Cookware " + Constants.HYPEN + Constants.AMENITIES_CLOSING_BRACE,
				result.get(0).getSubText());
	}

	@Test
	public void processFacilities_WithDisplayType2AndEmptySubAttributes_ShouldOnlyIncludeChildAttribute() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Kitchen");
		facility.setIconUrl("kitchen_icon");
		facility.setDisplayType("2");

		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility childAttr = new AttributesFacility();
		childAttr.setName("Cookware");
		childAttr.setSubAttributes(new ArrayList<>()); // Empty list

		childAttributes.add(childAttr);
		facility.setChildAttributes(childAttributes);

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Kitchen", result.get(0).getName());
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Cookware " + Constants.HYPEN + Constants.AMENITIES_CLOSING_BRACE,
				result.get(0).getSubText());
	}

	@Test
	public void processFacilities_WithChildAttributesButNoDisplayType_ShouldNotAddSubText() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Bathroom");
		facility.setIconUrl("bathroom_icon");
		// No display type

		List<AttributesFacility> childAttributes = new ArrayList<>();
		AttributesFacility attr = new AttributesFacility();
		attr.setName("Shower");
		childAttributes.add(attr);

		facility.setChildAttributes(childAttributes);

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Bathroom", result.get(0).getName());
		assertNull(result.get(0).getSubText());
	}

	@Test
	public void processFacilities_WithDisplayTypeButNoChildAttributes_ShouldNotAddSubText() {
		// Arrange
		Facility facility = new Facility();
		facility.setName("Bathroom");
		facility.setIconUrl("bathroom_icon");
		facility.setDisplayType("1");
		// No child attributes

		List<Facility> facilities = Collections.singletonList(facility);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(1, result.size());
		assertEquals("Bathroom", result.get(0).getName());
		assertNull(result.get(0).getSubText());
	}

	@Test
	public void processFacilities_WithMixedFacilities_ShouldProcessAllCorrectly() {
		// Arrange
		// First facility with display type 1
		Facility facility1 = new Facility();
		facility1.setName("Bathroom");
		facility1.setIconUrl("bathroom_icon");
		facility1.setDisplayType("1");

		List<AttributesFacility> childAttributes1 = new ArrayList<>();
		AttributesFacility attr1 = new AttributesFacility();
		attr1.setName("Shower");
		childAttributes1.add(attr1);
		facility1.setChildAttributes(childAttributes1);

		// Second facility with display type 2
		Facility facility2 = new Facility();
		facility2.setName("Kitchen");
		facility2.setIconUrl("kitchen_icon");
		facility2.setDisplayType("2");

		List<AttributesFacility> childAttributes2 = new ArrayList<>();
		AttributesFacility childAttr = new AttributesFacility();
		childAttr.setName("Cookware");

		List<com.mmt.model.SubAttributeFacility> subAttributes = new ArrayList<>();
		com.mmt.model.SubAttributeFacility sub = new com.mmt.model.SubAttributeFacility();
		sub.setName("Pots");
		subAttributes.add(sub);

		childAttr.setSubAttributes(subAttributes);
		childAttributes2.add(childAttr);
		facility2.setChildAttributes(childAttributes2);

		// Third facility with no child attributes
		Facility facility3 = new Facility();
		facility3.setName("WiFi");
		facility3.setIconUrl("wifi_icon");

		List<Facility> facilities = Arrays.asList(facility1, facility2, facility3);
		boolean isStrikeThrough = false;

		// Act
		List<SelectRoomFacility> result = ReflectionTestUtils.invokeMethod(
				commonResponseTransformer, "processFacilities", facilities, isStrikeThrough);

		// Assert
		assertNotNull(result);
		assertEquals(3, result.size());

		// Check first facility
		assertEquals("Bathroom", result.get(0).getName());
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Shower" + Constants.AMENITIES_CLOSING_BRACE,
				result.get(0).getSubText());

		// Check second facility
		assertEquals("Kitchen", result.get(1).getName());
		assertEquals(Constants.AMENITIES_OPEN_BRACE + "Cookware " + Constants.HYPEN + " Pots" + Constants.AMENITIES_CLOSING_BRACE,
				result.get(1).getSubText());

		// Check third facility
		assertEquals("WiFi", result.get(2).getName());
		assertNull(result.get(2).getSubText());
	}

	@Test
	public void testGetHighlightedAmenitiesV2() {
		// Test with null input
		List<HighlightedAmenity> result = commonResponseTransformer.getHighlightedAmenitiesV2(null);
		Assert.assertTrue(result.isEmpty());

		// Test with empty list
		result = commonResponseTransformer.getHighlightedAmenitiesV2(new ArrayList<>());
		Assert.assertTrue(result.isEmpty());

		// Test with valid input
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();
		FacilityGroup facilityGroup1 = new FacilityGroup();

		List<Facility> facilities1 = new ArrayList<>();
		Facility facility1 = new Facility();
		facility1.setName("WiFi");
		facility1.setIconUrl("wifi_icon.png");
		facilities1.add(facility1);

		Facility facility2 = new Facility();
		facility2.setName("Pool");
		facility2.setIconUrl("pool_icon.png");
		facilities1.add(facility2);

		facilityGroup1.setFacilities(facilities1);
		highlightedAmenities.add(facilityGroup1);

		FacilityGroup facilityGroup2 = new FacilityGroup();
		List<Facility> facilities2 = new ArrayList<>();
		Facility facility3 = new Facility();
		facility3.setName("Parking");
		facility3.setIconUrl("parking_icon.png");
		facilities2.add(facility3);
		facilityGroup2.setFacilities(facilities2);
		highlightedAmenities.add(facilityGroup2);

		result = commonResponseTransformer.getHighlightedAmenitiesV2(highlightedAmenities);

		// Assert the result has correct size
		Assert.assertEquals(3, result.size());

		// Assert the first amenity
		Assert.assertEquals("WiFi", result.get(0).getTitle());
		Assert.assertEquals("wifi_icon.png", result.get(0).getIconUrl());

		// Assert the second amenity
		Assert.assertEquals("Pool", result.get(1).getTitle());
		Assert.assertEquals("pool_icon.png", result.get(1).getIconUrl());

		// Assert the third amenity
		Assert.assertEquals("Parking", result.get(2).getTitle());
		Assert.assertEquals("parking_icon.png", result.get(2).getIconUrl());
	}

	@Test
	public void testGetHighlightedAmenitiesV2_WithEmptyFacilities() {
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		// Empty facilities list
		facilityGroup.setFacilities(new ArrayList<>());
		highlightedAmenities.add(facilityGroup);

		List<HighlightedAmenity> result = commonResponseTransformer.getHighlightedAmenitiesV2(highlightedAmenities);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void testGetHighlightedAmenitiesV2_WithNullFacilities() {
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();
		FacilityGroup facilityGroup = new FacilityGroup();
		// Null facilities list
		facilityGroup.setFacilities(null);
		highlightedAmenities.add(facilityGroup);

		List<HighlightedAmenity> result = commonResponseTransformer.getHighlightedAmenitiesV2(highlightedAmenities);
		Assert.assertTrue(result.isEmpty());
	}

	@Test
	public void testGetHighlightedAmenitiesV2_WithMixedFacilityGroups() {
		List<FacilityGroup> highlightedAmenities = new ArrayList<>();

		// First group with facilities
		FacilityGroup facilityGroup1 = new FacilityGroup();
		List<Facility> facilities1 = new ArrayList<>();
		Facility facility1 = new Facility();
		facility1.setName("WiFi");
		facility1.setIconUrl("wifi_icon.png");
		facilities1.add(facility1);
		facilityGroup1.setFacilities(facilities1);
		highlightedAmenities.add(facilityGroup1);

		// Second group with null facilities
		FacilityGroup facilityGroup2 = new FacilityGroup();
		facilityGroup2.setFacilities(null);
		highlightedAmenities.add(facilityGroup2);

		// Third group with empty facilities
		FacilityGroup facilityGroup3 = new FacilityGroup();
		facilityGroup3.setFacilities(new ArrayList<>());
		highlightedAmenities.add(facilityGroup3);

		// Fourth group with facilities
		FacilityGroup facilityGroup4 = new FacilityGroup();
		List<Facility> facilities4 = new ArrayList<>();
		Facility facility4 = new Facility();
		facility4.setName("Parking");
		facility4.setIconUrl("parking_icon.png");
		facilities4.add(facility4);
		facilityGroup4.setFacilities(facilities4);
		highlightedAmenities.add(facilityGroup4);

		List<HighlightedAmenity> result = commonResponseTransformer.getHighlightedAmenitiesV2(highlightedAmenities);

		// Should only have amenities from groups with non-empty facilities
		Assert.assertEquals(2, result.size());

		// Assert the first amenity
		Assert.assertEquals("WiFi", result.get(0).getTitle());
		Assert.assertEquals("wifi_icon.png", result.get(0).getIconUrl());

		// Assert the second amenity
		Assert.assertEquals("Parking", result.get(1).getTitle());
		Assert.assertEquals("parking_icon.png", result.get(1).getIconUrl());
	}

	@Test
	public void testBuildCardSheetCtaAction_WithNullInput() {
		// When
		com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetCtaAction", (Object) null);

		// Then
		assertNull("Should return null when input is null", result);
	}

	@Test
	public void testBuildCardSheetCtaAction_WithEmptyTitle() {
		// Given
		com.mmt.hotels.pojo.listing.personalization.CardAction input = new com.mmt.hotels.pojo.listing.personalization.CardAction();
		input.setTitle("");

		// When
		com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetCtaAction", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Title should be empty", "", result.getTitle());
	}

	@Test
	public void testBuildCardSheetCtaAction_WithNullTitle() {
		// Given
		com.mmt.hotels.pojo.listing.personalization.CardAction input = new com.mmt.hotels.pojo.listing.personalization.CardAction();
		input.setTitle(null);

		// When
		com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetCtaAction", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertNull("Title should be null", result.getTitle());
	}

	@Test
	public void testBuildCardSheetCtaAction_WithNullFilters() {
		// Given
		com.mmt.hotels.pojo.listing.personalization.CardAction input = new com.mmt.hotels.pojo.listing.personalization.CardAction();
		input.setTitle("Test Title");
		input.setFilters(null);

		// When
		com.mmt.hotels.clientgateway.response.moblanding.CardAction result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetCtaAction", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertNull("Filters should be null", result.getFilters());
	}


	// Tests for buildCardSheetInfoList method
	@Test
	public void testBuildCardSheetInfoList_WithNullInput() {
		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", (Object) null);

		// Then
		assertNull("Should return null when input is null", result);
	}

	@Test
	public void testBuildCardSheetInfoList_WithEmptyList() {
		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", new ArrayList<>());

		// Then
		assertNull("Should return null when input list is empty", result);
	}

	@Test
	public void testBuildCardSheetInfoList_WithSingleItem() {
		// Given
		GenericCardPayloadData item = new GenericCardPayloadData();
		item.setTitleText("Test Title");
		item.setIconUrl("test-icon-url");
		item.setSubText("Test SubText");
		List<GenericCardPayloadData> input = Collections.singletonList(item);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have one item", 1, result.size());

		GenericCardPayloadDataCG resultItem = result.get(0);
		assertEquals("Title should match", "Test Title", resultItem.getTitleText());
		assertEquals("Icon URL should match", "test-icon-url", resultItem.getIconUrl());
		assertEquals("SubText should match", "Test SubText", resultItem.getSubText());
	}

	@Test
	public void testBuildCardSheetInfoList_WithMultipleItems() {
		// Given
		GenericCardPayloadData item1 = new GenericCardPayloadData();
		item1.setTitleText("Title 1");
		item1.setIconUrl("icon-url-1");
		item1.setSubText("SubText 1");

		GenericCardPayloadData item2 = new GenericCardPayloadData();
		item2.setTitleText("Title 2");
		item2.setIconUrl("icon-url-2");
		item2.setSubText("SubText 2");

		List<GenericCardPayloadData> input = Arrays.asList(item1, item2);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have two items", 2, result.size());

		GenericCardPayloadDataCG resultItem1 = result.get(0);
		assertEquals("Title should match", "Title 1", resultItem1.getTitleText());
		assertEquals("Icon URL should match", "icon-url-1", resultItem1.getIconUrl());
		assertEquals("SubText should match", "SubText 1", resultItem1.getSubText());

		GenericCardPayloadDataCG resultItem2 = result.get(1);
		assertEquals("Title should match", "Title 2", resultItem2.getTitleText());
		assertEquals("Icon URL should match", "icon-url-2", resultItem2.getIconUrl());
		assertEquals("SubText should match", "SubText 2", resultItem2.getSubText());
	}

	@Test
	public void testBuildCardSheetInfoList_WithNullValues() {
		// Given
		GenericCardPayloadData item = new GenericCardPayloadData();
		item.setTitleText(null);
		item.setIconUrl(null);
		item.setSubText(null);
		List<GenericCardPayloadData> input = Collections.singletonList(item);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have one item", 1, result.size());

		GenericCardPayloadDataCG resultItem = result.get(0);
		assertNull("Title should be null", resultItem.getTitleText());
		assertNull("Icon URL should be null", resultItem.getIconUrl());
		assertNull("SubText should be null", resultItem.getSubText());
	}

	@Test
	public void testBuildCardSheetInfoList_WithMixedNullAndNonNullValues() {
		// Given
		GenericCardPayloadData item1 = new GenericCardPayloadData();
		item1.setTitleText("Title 1");
		item1.setIconUrl(null);
		item1.setSubText("SubText 1");

		GenericCardPayloadData item2 = new GenericCardPayloadData();
		item2.setTitleText(null);
		item2.setIconUrl("icon-url-2");
		item2.setSubText(null);

		List<GenericCardPayloadData> input = Arrays.asList(item1, item2);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have two items", 2, result.size());

		GenericCardPayloadDataCG resultItem1 = result.get(0);
		assertEquals("Title should match", "Title 1", resultItem1.getTitleText());
		assertNull("Icon URL should be null", resultItem1.getIconUrl());
		assertEquals("SubText should match", "SubText 1", resultItem1.getSubText());

		GenericCardPayloadDataCG resultItem2 = result.get(1);
		assertNull("Title should be null", resultItem2.getTitleText());
		assertEquals("Icon URL should match", "icon-url-2", resultItem2.getIconUrl());
		assertNull("SubText should be null", resultItem2.getSubText());
	}

	@Test
	public void testBuildCardSheetInfoList_EmptyStrings() {
		// Given
		GenericCardPayloadData item = new GenericCardPayloadData();
		item.setTitleText("");
		item.setIconUrl("");
		item.setSubText("");
		List<GenericCardPayloadData> input = Collections.singletonList(item);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have one item", 1, result.size());

		GenericCardPayloadDataCG resultItem = result.get(0);
		assertEquals("Title should be empty string", "", resultItem.getTitleText());
		assertEquals("Icon URL should be empty string", "", resultItem.getIconUrl());
		assertEquals("SubText should be empty string", "", resultItem.getSubText());
	}

	@Test
	public void testBuildCardSheetInfoList_ObjectReferences() {
		// Given
		GenericCardPayloadData item = new GenericCardPayloadData();
		item.setTitleText("Test Title");
		item.setIconUrl("test-icon-url");
		item.setSubText("Test SubText");
		List<GenericCardPayloadData> input = Collections.singletonList(item);

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertNotSame("Result list should be a different object", input, result);

		GenericCardPayloadDataCG resultItem = result.get(0);
		assertNotSame("Result item should be a different object", item, resultItem);
	}

	@Test
	public void testBuildCardSheetInfoList_PreservesOrder() {
		// Given
		List<GenericCardPayloadData> input = new ArrayList<>();
		for (int i = 0; i < 10; i++) {
			GenericCardPayloadData item = new GenericCardPayloadData();
			item.setTitleText("Title " + i);
			input.add(item);
		}

		// When
		List<GenericCardPayloadDataCG> result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "buildCardSheetInfoList", input);

		// Then
		assertNotNull("Result should not be null", result);
		assertEquals("Result should have the same size", input.size(), result.size());

		for (int i = 0; i < 10; i++) {
			assertEquals("Items should be in the same order",
					"Title " + i, result.get(i).getTitleText());
		}
	}

	@Test
	public void buildDrivingTimeText_ReturnsEmptyString_WhenDrivingTimeIsNull() {
		String result = commonResponseTransformer.buildDrivingTimeText(null);
		Assert.assertEquals(null, result);
	}

	@Test
	public void buildDrivingTimeText_ReturnsBucketMatchMinimumOnly_WhenInRange() {
		// Setup time buckets map to test single value bucket (no range, just minimum)
		Map<String, String> mockBucketMap = new HashMap<>();
		mockBucketMap.put("30", "CONST_30_MINS");
		ReflectionTestUtils.setField(commonResponseTransformer, "listingDrivingDurationBucketMap", mockBucketMap);

		when(polyglotService.getTranslatedData("CONST_30_MINS")).thenReturn("30 mins drive away");

		String result = commonResponseTransformer.buildDrivingTimeText(45.0);
		Assert.assertEquals("30 mins drive away", result);
	}

	@Test
	public void buildDrivingTimeText_ReturnsBucketMatchRange_WhenInRange() {
		// Setup time buckets map to test range bucket
		Map<String, String> mockBucketMap = new HashMap<>();
		mockBucketMap.put("30-60", "CONST_30_60_MINS");
		ReflectionTestUtils.setField(commonResponseTransformer, "listingDrivingDurationBucketMap", mockBucketMap);

		when(polyglotService.getTranslatedData("CONST_30_60_MINS")).thenReturn("30-60 mins drive away");

		String result = commonResponseTransformer.buildDrivingTimeText(45.0);
		Assert.assertEquals("30-60 mins drive away", result);
	}

	@Test
	public void buildDrivingTimeText_ReturnsNull_WhenNoMatchingBucket() {
		// Setup time buckets map with no matching bucket
		Map<String, String> mockBucketMap = new HashMap<>();
		mockBucketMap.put("5-10", "CONST_5_10_MINS");
		ReflectionTestUtils.setField(commonResponseTransformer, "listingDrivingDurationBucketMap", mockBucketMap);

		String result = commonResponseTransformer.buildDrivingTimeText(45.0);
		Assert.assertNull(result);
	}

	@Test
	public void isEligibleForNearbyFlow_ReturnsFalse_WhenDeviceDetailsIsNull() {
		boolean result = commonResponseTransformer.isEligibleForNearbyFlow(null);
		Assert.assertFalse(result);
	}

	@Test
	public void isEligibleForNearbyFlow_ReturnsTrue_WhenDeviceIsAndroid() {
		com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails = new com.mmt.hotels.clientgateway.request.DeviceDetails();
		deviceDetails.setBookingDevice(Constants.ANDROID);

		boolean result = commonResponseTransformer.isEligibleForNearbyFlow(deviceDetails);
		Assert.assertTrue(result);
	}

	@Test
	public void isEligibleForNearbyFlow_ReturnsTrue_WhenDeviceIsIOS() {
		com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails = new com.mmt.hotels.clientgateway.request.DeviceDetails();
		deviceDetails.setBookingDevice(Constants.DEVICE_IOS);

		boolean result = commonResponseTransformer.isEligibleForNearbyFlow(deviceDetails);
		Assert.assertTrue(result);
	}

	@Test
	public void isEligibleForNearbyFlow_ReturnsFalse_WhenDeviceIsNotMobile() {
		com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails = new com.mmt.hotels.clientgateway.request.DeviceDetails();
		deviceDetails.setBookingDevice("desktop");

		boolean result = commonResponseTransformer.isEligibleForNearbyFlow(deviceDetails);
		Assert.assertFalse(result);
	}

	@Test
	public void getInsuranceAndCharityAmount_ReturnsZero_WhenTotalPricingIsNull() {
		double result = ReflectionTestUtils.invokeMethod(commonResponseTransformer, "getInsuranceAndCharityAmount", (Object) null);
		Assert.assertEquals(0.0, result, 0.0);
	}

	@Test
	public void getGroupPriceTextForGRPNT_OneRoomOneNight() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ONE_ROOM_NEW_DESIGN))
			.thenReturn("{NIGHT_COUNT} night, {ROOM_COUNT} room");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(1, 1);
		Assert.assertEquals("1 night, 1 room", result);
	}

	@Test
	public void getGroupPriceTextForGRPNT_MultipleRoomsOneNight() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_NEW_DESIGN))
			.thenReturn("{NIGHT_COUNT} night, {ROOM_COUNT} rooms");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(3, 1);
		Assert.assertEquals("1 night, 3 rooms", result);
	}

	@Test
	public void getGroupPriceTextForGRPNT_OneRoomMultipleNights() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_ROOM_NEW_DESIGN))
			.thenReturn("{NIGHT_COUNT} nights, {ROOM_COUNT} room");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(1, 3);
		Assert.assertEquals("3 nights, 1 room", result);
	}

	@Test
	public void getGroupPriceTextForGRPNT_MultipleRoomsMultipleNights() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_NEW_DESIGN))
			.thenReturn("{NIGHT_COUNT} nights, {ROOM_COUNT} rooms");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(2, 3);
		Assert.assertEquals("3 nights, 2 rooms", result);
	}

	@Test
	public void getGroupPriceTextForGRPNT_NullRoomCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(null, 3);
		Assert.assertNull(result);
	}

	@Test
	public void getGroupPriceTextForGRPNT_NullNightCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNT(2, null);
		Assert.assertNull(result);
	}

	@Test
	public void getGroupPriceTextForGRPNTForAltAcco_OneNight() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO))
			.thenReturn("{NIGHT_COUNT} night");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(1);
		Assert.assertEquals("1 night", result);
	}

	@Test
	public void getGroupPriceTextForGRPNTForAltAcco_MultipleNights() {
		when(polyglotService.getTranslatedData(GROUP_PRICE_TEXT_MULTI_NIGHT_ALT_ACCO))
			.thenReturn("{NIGHT_COUNT} nights");

		String result = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(3);
		Assert.assertEquals("3 nights", result);
	}

	@Test
	public void getGroupPriceTextForGRPNTForAltAcco_NullNightCount() {
		String result = commonResponseTransformer.getGroupPriceTextForGRPNTForAltAcco(null);
		Assert.assertEquals(StringUtils.EMPTY, result);
	}

	@Test
	public void fetchNonBnplAppliedCouponDetails_NullTotalPricing() {
		Map<String, Object> result = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(null);

		Assert.assertNotNull(result);
		Assert.assertFalse((Boolean) result.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED));
		Assert.assertEquals("current", result.get(Constants.NON_BNPL_COUPON_APPLIED_CODE));
	}

	@Test
	public void fetchNonBnplAppliedCouponDetails_NullCoupons() {
		TotalPricing totalPricing = new TotalPricing();
		totalPricing.setCoupons(null);

		Map<String, Object> result = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(totalPricing);

		Assert.assertNotNull(result);
		Assert.assertFalse((Boolean) result.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED));
		Assert.assertEquals("current", result.get(Constants.NON_BNPL_COUPON_APPLIED_CODE));
	}

	@Test
	public void fetchNonBnplAppliedCouponDetails_WithNonBnplCoupon() {
		TotalPricing totalPricing = new TotalPricing();
		List<Coupon> coupons = new ArrayList<>();

		Coupon coupon = new Coupon();
		coupon.setAutoApplicable(true);
		coupon.setBnplAllowed(false);
		coupon.setCode("TESTCOUPON");
		coupons.add(coupon);

		totalPricing.setCoupons(coupons);

		Map<String, Object> result = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(totalPricing);

		Assert.assertNotNull(result);
		Assert.assertTrue((Boolean) result.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED));
		Assert.assertEquals("TESTCOUPON", result.get(Constants.NON_BNPL_COUPON_APPLIED_CODE));
	}

	@Test
	public void fetchNonBnplAppliedCouponDetails_WithBnplCoupon() {
		TotalPricing totalPricing = new TotalPricing();
		List<Coupon> coupons = new ArrayList<>();

		Coupon coupon = new Coupon();
		coupon.setAutoApplicable(true);
		coupon.setBnplAllowed(true);
		coupon.setCode("TESTCOUPON");
		coupons.add(coupon);

		totalPricing.setCoupons(coupons);

		Map<String, Object> result = commonResponseTransformer.fetchNonBnplAppliedCouponDetails(totalPricing);

		Assert.assertNotNull(result);
		Assert.assertFalse((Boolean) result.get(Constants.BNPL_DISABLED_DUE_TO_NON_BNPL_COUPON_APPLIED));
		Assert.assertEquals("current", result.get(Constants.NON_BNPL_COUPON_APPLIED_CODE));
	}

	@Test
	public void getBNPLDisabledReason_UserLevelBnplDisabled() {
		BNPLDisabledReason result = commonResponseTransformer.getBNPLDisabledReason(true, false, false);
		Assert.assertEquals(BNPLDisabledReason.ACTIVE_BOOKINGS_THRESHOLD, result);
	}

	@Test
	public void getBNPLDisabledReason_NonBnplCouponApplied() {
		BNPLDisabledReason result = commonResponseTransformer.getBNPLDisabledReason(false, true, false);
		Assert.assertEquals(BNPLDisabledReason.NON_BNPL_COUPON_APPLIED, result);
	}

	@Test
	public void getBNPLDisabledReason_InsuranceAddonSelected() {
		BNPLDisabledReason result = commonResponseTransformer.getBNPLDisabledReason(false, false, true);
		Assert.assertEquals(BNPLDisabledReason.INSURANCE_APPLIED, result);
	}

	@Test
	public void getBNPLDisabledReason_AllFalse() {
		BNPLDisabledReason result = commonResponseTransformer.getBNPLDisabledReason(false, false, false);
		Assert.assertNull(result);
	}

	@Test
	public void testBuildAdditionalCharges_withIhHouseRuleUiV2Enabled() {

		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("ihHouseRuleUIRevamp", "true");
		expDataMap.put("ihextrabnb", "true");

		AdditionalChargesBO inputBo = new AdditionalChargesBO
				.Builder()
				.buildHotelierCurrency("USD")
				.buildAdditionalFees(new ArrayList<>()).buildCityCode(Constants.CITY_CODE_MALDIVES)
				.buildPropertyType("test")
				.buildCountryCode("US")
				.build();

		Assert.assertNull(commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, PAGE_CONTEXT_DETAIL, expDataMap));
		inputBo.getAdditionalFees().add(new AdditionalFees());
		inputBo.getAdditionalFees().get(0).setAmount(0d);
		inputBo.getAdditionalFees().get(0).setPrice(new AdditionalFeesPrice());
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayRoom(1d);
		inputBo.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo.getAdditionalFees().get(0).getPrice().setPerStayChild(1d);
		inputBo.getAdditionalFees().get(0).setTotalChild(1);
		AdditionalMandatoryCharges charges = commonResponseTransformer.buildAdditionalCharges(inputBo, false, null, PAGE_CONTEXT_DETAIL, expDataMap);
		Assert.assertNotNull(charges);
		Assert.assertEquals("IMPORTANT_INFORMATION_SUBTITLE_IH", charges.getSubTitle());
		Assert.assertEquals("mandatory_charges_section_desc", charges.getDescription());

		AdditionalChargesBO inputBo1 = new AdditionalChargesBO
				.Builder()
				.buildHotelierCurrency("USD")
				.buildAdditionalFees(new ArrayList<>()).buildCityCode(Constants.CITY_CODE_MALDIVES)
				.buildPropertyType("test")
				.buildRoomName("Deluxe Room")
				.buildCountryCode("US")
				.build();

		Assert.assertNull(commonResponseTransformer.buildAdditionalCharges(inputBo1, false, null, PAGE_CONTEXT_DETAIL, expDataMap));
		inputBo1.getAdditionalFees().add(new AdditionalFees());
		inputBo1.getAdditionalFees().get(0).setAmount(0d);
		inputBo1.getAdditionalFees().get(0).setPrice(new AdditionalFeesPrice());
		inputBo1.getAdditionalFees().get(0).getPrice().setPerStayRoom(1d);
		inputBo1.getAdditionalFees().get(0).setTotalRooms(1);
		inputBo1.getAdditionalFees().get(0).getPrice().setPerStayAdult(1d);
		inputBo1.getAdditionalFees().get(0).setTotalAdults(1);
		inputBo1.getAdditionalFees().get(0).getPrice().setPerStayChild(1d);
		inputBo1.getAdditionalFees().get(0).setTotalChild(1);
		AdditionalMandatoryCharges charges1 = commonResponseTransformer.buildAdditionalCharges(inputBo1, false, null, PAGE_CONTEXT_DETAIL, expDataMap);
		Assert.assertNotNull(charges1);
		Assert.assertEquals("IMPORTANT_INFORMATION_SUBTITLE_IH", charges1.getSubTitle());
		Assert.assertTrue(charges1.getDescription().contains("mandatory_charges_section_desc"));
		Assert.assertTrue(charges1.getDescription().contains("MANDATORY_CHARGES_ROOM_DESC"));
	}

	@Test
	public void testSanitiseHeroCardData_bothNull() {
		// Should not throw any exception
		CommonResponseTransformer.sanitiseHeroCardData(null, null);
	}

	@Test
	public void testSanitiseHeroCardData_myPartnerHeroNull() {
		RoomTypeDetails roomTypeDetails = mock(RoomTypeDetails.class);
		CommonResponseTransformer.sanitiseHeroCardData(roomTypeDetails, null);
	}

	@Test
	public void testSanitiseHeroCardData_roomTypeDetailsNull() {
		MyPartnerLoyaltyResponse myPartnerHero = mock(com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse.class);
		CommonResponseTransformer.sanitiseHeroCardData(null, myPartnerHero);
	}

	@Test
	public void testSanitiseHeroCardData_secondaryCardsNull() {
		RoomTypeDetails roomTypeDetails = mock(RoomTypeDetails.class);
		MyPartnerLoyaltyResponse myPartnerHero = mock(com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse.class);
		when(myPartnerHero.getSecondaryCards()).thenReturn(null);
		CommonResponseTransformer.sanitiseHeroCardData(roomTypeDetails, myPartnerHero);
	}

	@Test
	public void testSanitiseHeroCardData_updatesCardFields() {
		// Arrange
		RoomTypeDetails roomTypeDetails = mock(RoomTypeDetails.class);
		DisplayFare totalDisplayFare = mock(com.mmt.hotels.model.response.pricing.DisplayFare.class);
		HeroTierUpgradeDetails heroTierUpgradeDetails = mock(com.mmt.hotels.model.response.pricing.HeroTierUpgradeDetails.class);
		when(roomTypeDetails.getTotalDisplayFare()).thenReturn(totalDisplayFare);
		when(totalDisplayFare.getHeroTierUpgradeDetails()).thenReturn(heroTierUpgradeDetails);
		when(heroTierUpgradeDetails.getTierCashbackMessage()).thenReturn("Cashback!");
		when(heroTierUpgradeDetails.getTierUpgradeMessage()).thenReturn("Upgrade!");

		MyPartnerHeroCard card = mock(com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard.class);
		when(card.getCardId()).thenReturn(Constants.NEXT_TIER_BANNER_FAREBREAKUP);
		List<com.mmt.hotels.model.response.persuasion.MyPartnerHeroCard> cards = new ArrayList<>();
		cards.add(card);
		MyPartnerLoyaltyResponse myPartnerHero = mock(com.mmt.hotels.model.response.persuasion.MyPartnerLoyaltyResponse.class);
		when(myPartnerHero.getSecondaryCards()).thenReturn(cards);

		// Act
		CommonResponseTransformer.sanitiseHeroCardData(roomTypeDetails, myPartnerHero);

		// Assert
		Mockito.verify(card).setMessageTitle("Cashback!");
		Mockito.verify(card).setMessageText("Upgrade!");
	}
	@Test
	public void testCanShowIndex() {
		// Set up the showIndexSectionList
		ReflectionTestUtils.setField(commonResponseTransformer, "showIndexSectionList", Arrays.asList("MOST_BOOKED_HOTELS", "RECOMMENDED_HOTELS"));

		// Set up the sectionName that is present in the list
		String sectionName = "MOST_BOOKED_HOTELS";
		String notPresentSection = "NOT_PRESENT_SECTION";

		// Set up expDataMap with the experiment enabled
		Map<String, String> expDataMap = new HashMap<>();
		expDataMap.put("mostbookedrank", "t");

		// Mock utility.isExperimentOn to return true for the experiment
		Mockito.when(utility.isExperimentOn(expDataMap, "mostbookedrank")).thenReturn(true);

		// Should return true when all conditions are met
		boolean result = commonResponseTransformer.canShowIndex(sectionName, expDataMap);
		Assert.assertTrue(result);

		// Should return false if sectionName is not in the list
		boolean resultNotPresent = commonResponseTransformer.canShowIndex(notPresentSection, expDataMap);
		Assert.assertFalse(resultNotPresent);

		// Should return false if experiment is off
		Mockito.when(utility.isExperimentOn(expDataMap, "mostbookedrank")).thenReturn(false);
		boolean resultExpOff = commonResponseTransformer.canShowIndex(sectionName, expDataMap);
		Assert.assertFalse(resultExpOff);

		// Should return false if sectionName is blank
		boolean resultBlank = commonResponseTransformer.canShowIndex("", expDataMap);
		Assert.assertFalse(resultBlank);

		// Should return false if showIndexSectionList is empty
		ReflectionTestUtils.setField(commonResponseTransformer, "showIndexSectionList", Collections.emptyList());
		Mockito.when(utility.isExperimentOn(expDataMap, "mostbookedrank")).thenReturn(true);
		boolean resultEmptyList = commonResponseTransformer.canShowIndex(sectionName, expDataMap);
		Assert.assertFalse(resultEmptyList);
	}

	@Test
	public void testBuildEffectivePrice_WithPositiveEffectivePrice() {
		// Arrange
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		displayPriceBrkDwn.setEffectivePrice(100.0d);

		when(polyglotService.getTranslatedData(EFFECTIVE_PRICE_LABEL))
				.thenReturn("Effective Price");
		when(polyglotService.getTranslatedData(PRICE_TYPE_SUM))
				.thenReturn("Sum");

		// Act
		PricingDetails result = commonResponseTransformer.buildEffectivePrice(displayPriceBrkDwn);

		// Assert
		assertNotNull(result);
		assertEquals(100.0d, result.getAmount(), 0.001);
		assertEquals("EFFECTIVE_PRICE", result.getKey());
		assertEquals("Effective Price", result.getLabel());
		assertEquals("Sum", result.getType());
	}

	@Test
	public void testBuildEffectivePrice_WithZeroEffectivePrice() {
		// Arrange
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		displayPriceBrkDwn.setEffectivePrice(0.0d);

		// Act
		PricingDetails result = commonResponseTransformer.buildEffectivePrice(displayPriceBrkDwn);

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildEffectivePrice_WithNegativeEffectivePrice() {
		// Arrange
		DisplayPriceBreakDown displayPriceBrkDwn = new DisplayPriceBreakDown();
		displayPriceBrkDwn.setEffectivePrice(-50.0d);

		// Act
		PricingDetails result = commonResponseTransformer.buildEffectivePrice(displayPriceBrkDwn);

		// Assert
		assertNull(result);
	}

	@Test
	public void testBuildEffectivePrice_WithNullDisplayPriceBreakDown() {
		// Arrange
		DisplayPriceBreakDown displayPriceBrkDwn = null;

		// Act
		PricingDetails result = commonResponseTransformer.buildEffectivePrice(displayPriceBrkDwn);

		// Assert
		assertNull(result);
	}

	@Test
	public void should_BuildLocationData_When_LocationDataListIsProvided() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.LocationData> locationDataListHES = new ArrayList<>();
		
		com.mmt.hotels.model.response.altaccodata.LocationData locationData1 = new com.mmt.hotels.model.response.altaccodata.LocationData();
		locationData1.setTitle("Test Location 1");
		locationData1.setDescription("Test Description 1");
		locationData1.setImageUrl("https://example.com/image1.jpg");
		locationData1.setVideoUrl("https://example.com/video1.mp4");
		locationData1.setDeeplink("https://example.com/deeplink1");
		locationData1.setThumbnailUrl("https://example.com/thumb1.jpg");
		
		List<com.mmt.hotels.model.response.altaccodata.InfoItem> infoList1 = new ArrayList<>();
		com.mmt.hotels.model.response.altaccodata.InfoItem infoItem1 = new com.mmt.hotels.model.response.altaccodata.InfoItem();
		infoItem1.setIconUrl("https://example.com/icon1.png");
		infoItem1.setTitle("Info Title 1");
		infoList1.add(infoItem1);
		locationData1.setInfoList(infoList1);
		
		locationDataListHES.add(locationData1);

		// When - using reflection to access private method
		Method buildLocationDataMethod = CommonResponseTransformer.class.getDeclaredMethod("buildLocationData", List.class);
		buildLocationDataMethod.setAccessible(true);
		List<LocationDataCG> result = (List<LocationDataCG>) buildLocationDataMethod.invoke(commonResponseTransformer, locationDataListHES);

		// Then
		Assert.assertNotNull(result);
		Assert.assertEquals(1, result.size());
		
		LocationDataCG resultLocationData = result.get(0);
		Assert.assertEquals("Test Location 1", resultLocationData.getTitle());
		Assert.assertEquals("Test Description 1", resultLocationData.getDescription());
		Assert.assertEquals("https://example.com/image1.jpg", resultLocationData.getImageUrl());
		Assert.assertEquals("https://example.com/video1.mp4", resultLocationData.getVideoUrl());
		Assert.assertEquals("https://example.com/deeplink1", resultLocationData.getDeeplink());
		Assert.assertEquals("https://example.com/thumb1.jpg", resultLocationData.getThumbnailUrl());
		Assert.assertNotNull(resultLocationData.getInfoList());
		Assert.assertEquals(1, resultLocationData.getInfoList().size());
		Assert.assertEquals("https://example.com/icon1.png", resultLocationData.getInfoList().get(0).getIconUrl());
		Assert.assertEquals("Info Title 1", resultLocationData.getInfoList().get(0).getTitle());
	}

	@Test
	public void should_ReturnNull_When_LocationDataListIsEmpty() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.LocationData> emptyLocationDataList = new ArrayList<>();

		// When - using reflection to access private method
		Method buildLocationDataMethod = CommonResponseTransformer.class.getDeclaredMethod("buildLocationData", List.class);
		buildLocationDataMethod.setAccessible(true);
		List<LocationDataCG> result = (List<LocationDataCG>) buildLocationDataMethod.invoke(commonResponseTransformer, emptyLocationDataList);

		// Then
		Assert.assertNull(result);
	}

	@Test
	public void should_ReturnNull_When_LocationDataListIsNull() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.LocationData> nullLocationDataList = null;

		// When - using reflection to access private method
		Method buildLocationDataMethod = CommonResponseTransformer.class.getDeclaredMethod("buildLocationData", List.class);
		buildLocationDataMethod.setAccessible(true);
		List<LocationDataCG> result = (List<LocationDataCG>) buildLocationDataMethod.invoke(commonResponseTransformer, nullLocationDataList);

		// Then
		Assert.assertNull(result);
	}

	@Test
	public void should_BuildInfoItemList_When_InfoListIsProvided() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.InfoItem> infoListHES = new ArrayList<>();
		
		com.mmt.hotels.model.response.altaccodata.InfoItem infoItem1 = new com.mmt.hotels.model.response.altaccodata.InfoItem();
		infoItem1.setIconUrl("https://example.com/icon1.png");
		infoItem1.setTitle("Info Title 1");
		infoListHES.add(infoItem1);
		
		com.mmt.hotels.model.response.altaccodata.InfoItem infoItem2 = new com.mmt.hotels.model.response.altaccodata.InfoItem();
		infoItem2.setIconUrl("https://example.com/icon2.png");
		infoItem2.setTitle("Info Title 2");
		infoListHES.add(infoItem2);

		// When - using reflection to access private method
		Method buildInfoItemListMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItemList", List.class);
		buildInfoItemListMethod.setAccessible(true);
		List<InfoItem> result = (List<InfoItem>) buildInfoItemListMethod.invoke(commonResponseTransformer, infoListHES);

		// Then
		Assert.assertNotNull(result);
		Assert.assertEquals(2, result.size());
		
		InfoItem resultInfoItem1 = result.get(0);
		Assert.assertEquals("https://example.com/icon1.png", resultInfoItem1.getIconUrl());
		Assert.assertEquals("Info Title 1", resultInfoItem1.getTitle());
		
		InfoItem resultInfoItem2 = result.get(1);
		Assert.assertEquals("https://example.com/icon2.png", resultInfoItem2.getIconUrl());
		Assert.assertEquals("Info Title 2", resultInfoItem2.getTitle());
	}

	@Test
	public void should_ReturnNull_When_InfoListIsEmpty() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.InfoItem> emptyInfoList = new ArrayList<>();

		// When - using reflection to access private method
		Method buildInfoItemListMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItemList", List.class);
		buildInfoItemListMethod.setAccessible(true);
		List<InfoItem> result = (List<InfoItem>) buildInfoItemListMethod.invoke(commonResponseTransformer, emptyInfoList);

		// Then
		Assert.assertNull(result);
	}

	@Test
	public void should_ReturnNull_When_InfoListIsNull() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.InfoItem> nullInfoList = null;

		// When - using reflection to access private method
		Method buildInfoItemListMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItemList", List.class);
		buildInfoItemListMethod.setAccessible(true);
		List<InfoItem> result = (List<InfoItem>) buildInfoItemListMethod.invoke(commonResponseTransformer, nullInfoList);

		// Then
		Assert.assertNull(result);
	}

	@Test
	public void should_BuildInfoItem_When_InfoItemIsProvided() throws Exception {
		// Given
		com.mmt.hotels.model.response.altaccodata.InfoItem infoItemHES = new com.mmt.hotels.model.response.altaccodata.InfoItem();
		infoItemHES.setIconUrl("https://example.com/icon.png");
		infoItemHES.setTitle("Test Info Title");

		// When - using reflection to access private method
		Method buildInfoItemMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItem", com.mmt.hotels.model.response.altaccodata.InfoItem.class);
		buildInfoItemMethod.setAccessible(true);
		InfoItem result = (InfoItem) buildInfoItemMethod.invoke(commonResponseTransformer, infoItemHES);

		// Then
		Assert.assertNotNull(result);
		Assert.assertEquals("https://example.com/icon.png", result.getIconUrl());
		Assert.assertEquals("Test Info Title", result.getTitle());
	}

	@Test
	public void should_ReturnNull_When_InfoItemIsNull() throws Exception {
		// Given
		com.mmt.hotels.model.response.altaccodata.InfoItem nullInfoItem = null;

		// When - using reflection to access private method
		Method buildInfoItemMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItem", com.mmt.hotels.model.response.altaccodata.InfoItem.class);
		buildInfoItemMethod.setAccessible(true);
		InfoItem result = (InfoItem) buildInfoItemMethod.invoke(commonResponseTransformer, nullInfoItem);

		// Then
		Assert.assertNull(result);
	}

	@Test
	public void should_FilterOutNullInfoItems_When_BuildingInfoItemList() throws Exception {
		// Given
		List<com.mmt.hotels.model.response.altaccodata.InfoItem> infoListHES = new ArrayList<>();
		
		// Add valid info item
		com.mmt.hotels.model.response.altaccodata.InfoItem validInfoItem = new com.mmt.hotels.model.response.altaccodata.InfoItem();
		validInfoItem.setIconUrl("https://example.com/icon.png");
		validInfoItem.setTitle("Valid Info Title");
		infoListHES.add(validInfoItem);
		
		// Add null info item
		infoListHES.add(null);

		// When - using reflection to access private method
		Method buildInfoItemListMethod = CommonResponseTransformer.class.getDeclaredMethod("buildInfoItemList", List.class);
		buildInfoItemListMethod.setAccessible(true);
		List<InfoItem> result = (List<InfoItem>) buildInfoItemListMethod.invoke(commonResponseTransformer, infoListHES);

		// Then
		Assert.assertNotNull(result);
		Assert.assertEquals(1, result.size()); // Only the valid item should be included
		Assert.assertEquals("https://example.com/icon.png", result.get(0).getIconUrl());
		Assert.assertEquals("Valid Info Title", result.get(0).getTitle());
	}
}
